<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🌐 AI智能翻译 - 专业的多语言翻译工具</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🌐</text></svg>">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            /* 现代化色彩系统 */
            --primary-color: #6366f1;
            --primary-hover: #4f46e5;
            --primary-light: #a5b4fc;
            --secondary-color: #64748b;
            --success-color: #10b981;
            --success-hover: #059669;
            --warning-color: #f59e0b;
            --error-color: #ef4444;

            /* 背景色彩 */
            --bg-primary: #ffffff;
            --bg-secondary: #f8fafc;
            --bg-tertiary: #f1f5f9;
            --bg-glass: rgba(255, 255, 255, 0.25);
            --bg-glass-hover: rgba(255, 255, 255, 0.35);

            /* 文字色彩 */
            --text-primary: #1e293b;
            --text-secondary: #475569;
            --text-muted: #64748b;
            --text-light: #94a3b8;

            /* 边框色彩 */
            --border-color: rgba(226, 232, 240, 0.8);
            --border-hover: rgba(203, 213, 225, 0.9);
            --border-focus: rgba(99, 102, 241, 0.5);

            /* 阴影系统 */
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            --shadow-glass: 0 8px 32px 0 rgba(31, 38, 135, 0.37);

            /* 圆角系统 */
            --border-radius: 16px;
            --border-radius-lg: 20px;
            --border-radius-xl: 24px;
            --border-radius-2xl: 32px;

            /* 动画系统 */
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            --transition-fast: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
            --transition-slow: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);

            /* 渐变系统 */
            --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --gradient-bg: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            --gradient-glass: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: var(--gradient-bg);
            background-size: 400% 400%;
            animation: gradientShift 15s ease infinite;
            color: var(--text-primary);
            line-height: 1.6;
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
        }

        /* 动态背景动画 */
        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        /* 背景装饰元素 */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }

        .header {
            background: var(--bg-glass);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--border-color);
            padding: 1.5rem 2rem;
            position: sticky;
            top: 0;
            z-index: 100;
            box-shadow: var(--shadow-lg);
            border-radius: 0 0 var(--border-radius-xl) var(--border-radius-xl);
            margin: 0 1rem;
        }

        .header-content {
            max-width: 1400px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-color);
        }

        .logo-icon {
            font-size: 2rem;
            animation: rotate 20s linear infinite;
        }

        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .model-control {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .model-label {
            font-size: 0.9rem;
            color: var(--text-secondary);
            font-weight: 500;
        }

        .model-select {
            padding: 0.75rem 1rem;
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius);
            background: var(--white);
            font-size: 0.9rem;
            color: var(--text-primary);
            min-width: 200px;
            transition: var(--transition);
            cursor: pointer;
        }

        .model-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        .model-select:disabled {
            background: var(--bg-secondary);
            color: var(--text-muted);
            cursor: not-allowed;
        }

        .refresh-btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: var(--border-radius);
            background: var(--primary-color);
            color: var(--white);
            font-weight: 600;
            font-size: 0.9rem;
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .refresh-btn:hover:not(:disabled) {
            background: var(--primary-hover);
            transform: translateY(-1px);
        }

        .refresh-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .main-container {
            max-width: 1400px;
            margin: 2rem auto;
            padding: 0 2rem;
        }

        .translation-workspace {
            background: var(--bg-glass);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius-2xl);
            box-shadow: var(--shadow-glass);
            overflow: hidden;
            position: relative;
        }

        /* 玻璃态效果增强 */
        .translation-workspace::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
            z-index: 1;
        }

        .workspace-header {
            padding: 3rem 2rem 2rem;
            text-align: center;
            background: var(--gradient-glass);
            border-bottom: 1px solid var(--border-color);
            position: relative;
            overflow: hidden;
        }

        /* 头部装饰效果 */
        .workspace-header::after {
            content: '';
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 4px;
            background: var(--gradient-primary);
            border-radius: 0 0 var(--border-radius) var(--border-radius);
        }

        .workspace-title {
            font-size: 2.5rem;
            font-weight: 800;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 1rem;
            letter-spacing: -0.02em;
        }

        .workspace-subtitle {
            color: var(--text-secondary);
            font-size: 1.2rem;
            font-weight: 500;
            opacity: 0.9;
        }

        .realtime-indicator {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            margin-top: 1rem;
            padding: 0.5rem 1rem;
            background: rgba(37, 99, 235, 0.1);
            border-radius: 20px;
            font-size: 0.85rem;
            color: var(--primary-color);
            font-weight: 500;
        }

        .realtime-dot {
            width: 8px;
            height: 8px;
            background: var(--success-color);
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .language-control {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 2.5rem 2rem;
            border-bottom: 1px solid var(--border-color);
            gap: 3rem;
            flex-wrap: wrap;
            background: var(--gradient-glass);
            position: relative;
        }

        .language-section {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 1rem;
        }

        .language-label {
            font-size: 1rem;
            font-weight: 600;
            color: var(--text-secondary);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .language-btn {
            padding: 1.2rem 3rem;
            border: 2px solid var(--border-color);
            border-radius: 50px;
            background: var(--bg-glass);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            color: var(--text-primary);
            font-weight: 600;
            font-size: 1.1rem;
            cursor: pointer;
            transition: var(--transition);
            box-shadow: var(--shadow-md);
            min-width: 160px;
            position: relative;
            overflow: hidden;
        }

        .language-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            transition: left 0.5s;
        }

        .language-btn:hover::before {
            left: 100%;
        }

        .language-btn.active {
            background: var(--gradient-primary);
            border-color: transparent;
            color: white;
            box-shadow: var(--shadow-lg);
            transform: scale(1.05);
        }

        .language-btn:hover:not(.active) {
            border-color: var(--primary-color);
            background: var(--bg-glass-hover);
            transform: translateY(-3px);
            box-shadow: var(--shadow-lg);
        }

        .swap-btn {
            background: var(--gradient-success);
            color: white;
            border: none;
            border-radius: 50%;
            width: 80px;
            height: 80px;
            font-size: 2rem;
            cursor: pointer;
            transition: var(--transition);
            box-shadow: var(--shadow-xl);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        .swap-btn::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            transition: var(--transition);
            transform: translate(-50%, -50%);
        }

        .swap-btn:hover::before {
            width: 100%;
            height: 100%;
        }

        .swap-btn:hover {
            transform: rotate(180deg) scale(1.15);
            box-shadow: var(--shadow-2xl);
        }

        .translation-panels {
            display: grid;
            grid-template-columns: 1fr 1fr;
            min-height: 600px;
        }

        .text-panel {
            position: relative;
            display: flex;
            flex-direction: column;
        }

        .text-panel:first-child {
            border-right: 1px solid var(--border-color);
        }

        .panel-header {
            padding: 2.5rem 3rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid var(--border-color);
            background: var(--gradient-glass);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }

        .panel-title {
            font-weight: 700;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 1.1rem;
        }

        .char-counter {
            font-size: 0.9rem;
            color: var(--text-muted);
            font-weight: 500;
        }

        .text-area {
            flex: 1;
            padding: 3rem;
            border: none;
            resize: none;
            font-size: 1.2rem;
            line-height: 1.8;
            background: transparent;
            color: var(--text-primary);
            outline: none;
            font-family: inherit;
            min-height: 500px;
            transition: var(--transition);
        }

        .text-area::placeholder {
            color: var(--text-light);
            font-style: italic;
            font-weight: 400;
        }

        .text-area:focus {
            background: rgba(99, 102, 241, 0.03);
            transform: scale(1.002);
        }

        .output-panel {
            background: rgba(99, 102, 241, 0.02);
            position: relative;
        }

        .output-panel::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--gradient-glass);
            opacity: 0.5;
            pointer-events: none;
        }

        .output-panel .text-area {
            background: transparent;
            cursor: text;
            color: var(--text-secondary);
            position: relative;
            z-index: 1;
        }

        .panel-actions {
            position: absolute;
            bottom: 1.5rem;
            right: 1.5rem;
            display: flex;
            gap: 0.75rem;
        }

        .action-btn {
            background: var(--bg-glass);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: 1rem;
            cursor: pointer;
            transition: var(--transition);
            font-size: 1.2rem;
            width: 48px;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: var(--shadow-md);
            position: relative;
            overflow: hidden;
        }

        .action-btn::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: var(--gradient-primary);
            border-radius: 50%;
            transition: var(--transition);
            transform: translate(-50%, -50%);
            z-index: -1;
        }

        .action-btn:hover::before {
            width: 200%;
            height: 200%;
        }

        .action-btn:hover {
            color: white;
            transform: translateY(-3px) scale(1.05);
            box-shadow: var(--shadow-lg);
            border-color: transparent;
        }

        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.95);
            display: none;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            gap: 1.5rem;
            backdrop-filter: blur(4px);
        }

        .loading-overlay.show {
            display: flex;
        }

        .spinner {
            width: 50px;
            height: 50px;
            border: 4px solid var(--border-color);
            border-left: 4px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .loading-text {
            color: var(--text-secondary);
            font-weight: 600;
            font-size: 1.1rem;
        }

        .controls-section {
            padding: 3rem;
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 2rem;
            flex-wrap: wrap;
            background: var(--gradient-glass);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }

        .translate-btn {
            background: var(--gradient-primary);
            color: white;
            border: none;
            padding: 1.5rem 5rem;
            border-radius: 50px;
            font-size: 1.3rem;
            font-weight: 700;
            cursor: pointer;
            transition: var(--transition);
            box-shadow: var(--shadow-xl);
            display: flex;
            align-items: center;
            gap: 1rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            position: relative;
            overflow: hidden;
        }

        .translate-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            transition: left 0.6s;
        }

        .translate-btn:hover::before {
            left: 100%;
        }

        .translate-btn:hover:not(:disabled) {
            transform: translateY(-4px) scale(1.05);
            box-shadow: var(--shadow-2xl);
        }

        .translate-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .realtime-toggle {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1.2rem 2rem;
            background: var(--bg-glass);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 2px solid var(--border-color);
            border-radius: 50px;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            box-shadow: var(--shadow-md);
            position: relative;
            overflow: hidden;
        }

        .realtime-toggle::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--gradient-success);
            opacity: 0;
            transition: var(--transition);
        }

        .realtime-toggle.active::before {
            opacity: 1;
        }

        .realtime-toggle.active {
            border-color: transparent;
            color: white;
            box-shadow: var(--shadow-lg);
        }

        .realtime-toggle.active span {
            position: relative;
            z-index: 1;
        }

        .realtime-toggle:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .message {
            margin: 1.5rem 2rem;
            padding: 1.25rem 2rem;
            border-radius: var(--border-radius-lg);
            display: none;
            align-items: center;
            gap: 1rem;
            font-weight: 600;
            border: 1px solid transparent;
            font-size: 1rem;
        }

        .message.show {
            display: flex;
        }

        .message.success {
            background: linear-gradient(135deg, #f0fdf4, #dcfce7);
            border-color: #bbf7d0;
            color: #166534;
        }

        .message.error {
            background: linear-gradient(135deg, #fef2f2, #fee2e2);
            border-color: #fecaca;
            color: #dc2626;
        }

        .message.info {
            background: linear-gradient(135deg, #eff6ff, #dbeafe);
            border-color: #bfdbfe;
            color: #1e40af;
        }

        .footer {
            text-align: center;
            padding: 4rem 2rem;
            color: var(--text-light);
            font-size: 1rem;
            background: var(--gradient-glass);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border-top: 1px solid var(--border-color);
            margin: 2rem 1rem 0;
            border-radius: var(--border-radius-xl) var(--border-radius-xl) 0 0;
        }

        .footer p {
            margin-bottom: 0.5rem;
            opacity: 0.8;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .header {
                margin: 0;
                border-radius: 0;
            }

            .header-content {
                flex-direction: column;
                text-align: center;
                gap: 1.5rem;
            }

            .model-control {
                flex-direction: column;
                width: 100%;
                gap: 1rem;
            }

            .model-select {
                width: 100%;
            }

            .main-container {
                padding: 0 1rem;
                margin: 1rem auto;
            }

            .translation-workspace {
                border-radius: var(--border-radius-xl);
            }

            .translation-panels {
                grid-template-columns: 1fr;
                min-height: 800px;
            }

            .text-panel:first-child {
                border-right: none;
                border-bottom: 1px solid var(--border-color);
            }

            .language-control {
                flex-direction: column;
                gap: 2rem;
                padding: 2rem 1rem;
            }

            .swap-btn {
                width: 70px;
                height: 70px;
                font-size: 1.8rem;
            }

            .controls-section {
                flex-direction: column;
                padding: 2rem 1rem;
            }

            .translate-btn {
                width: 100%;
                justify-content: center;
                padding: 1.2rem 2rem;
                font-size: 1.1rem;
            }

            .text-area {
                padding: 2rem;
                min-height: 300px;
                font-size: 1.1rem;
            }

            .workspace-title {
                font-size: 2rem;
            }

            .workspace-subtitle {
                font-size: 1.1rem;
            }

            .language-btn {
                padding: 1rem 2.5rem;
                min-width: 140px;
                font-size: 1rem;
            }

            .footer {
                margin: 2rem 0 0;
                border-radius: 0;
            }
        }

        /* 平板设备优化 */
        @media (max-width: 1024px) and (min-width: 769px) {
            .main-container {
                padding: 0 1.5rem;
            }

            .language-control {
                gap: 2rem;
            }

            .translate-btn {
                padding: 1.3rem 4rem;
            }
        }

        /* 动画效果 */
        .fade-in {
            animation: fadeIn 0.6s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* 焦点可见性提升 */
        .text-area:focus,
        .model-select:focus,
        .translate-btn:focus,
        .action-btn:focus {
            outline: 3px solid rgba(37, 99, 235, 0.3);
            outline-offset: 2px;
        }

        /* 翻译状态指示 */
        .translating {
            position: relative;
        }

        .translating::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gradient-primary);
            background-size: 200% 100%;
            animation: shimmer 2s infinite;
            border-radius: 0 0 var(--border-radius) var(--border-radius);
        }

        @keyframes shimmer {
            0% { background-position: -200% 0; }
            100% { background-position: 200% 0; }
        }

        /* 滚动条美化 */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--bg-secondary);
            border-radius: var(--border-radius);
        }

        ::-webkit-scrollbar-thumb {
            background: var(--gradient-primary);
            border-radius: var(--border-radius);
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--primary-hover);
        }

        /* 选择文本样式 */
        ::selection {
            background: rgba(99, 102, 241, 0.3);
            color: var(--text-primary);
        }

        /* 焦点可见性增强 */
        .text-area:focus-visible,
        .model-select:focus-visible,
        .translate-btn:focus-visible,
        .action-btn:focus-visible,
        .language-btn:focus-visible,
        .realtime-toggle:focus-visible {
            outline: 3px solid var(--border-focus);
            outline-offset: 2px;
        }

        /* 加载动画增强 */
        .spinner {
            width: 60px;
            height: 60px;
            border: 4px solid var(--border-color);
            border-left: 4px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        /* 消息样式增强 */
        .message {
            margin: 1.5rem 2rem;
            padding: 1.5rem 2rem;
            border-radius: var(--border-radius-lg);
            display: none;
            align-items: center;
            gap: 1rem;
            font-weight: 600;
            border: 1px solid transparent;
            font-size: 1rem;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }

        .message.show {
            display: flex;
            animation: slideIn 0.3s ease-out;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .message.success {
            background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(5, 150, 105, 0.1));
            border-color: rgba(16, 185, 129, 0.3);
            color: #065f46;
        }

        .message.error {
            background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(220, 38, 38, 0.1));
            border-color: rgba(239, 68, 68, 0.3);
            color: #991b1b;
        }

        .message.info {
            background: linear-gradient(135deg, rgba(99, 102, 241, 0.1), rgba(79, 70, 229, 0.1));
            border-color: rgba(99, 102, 241, 0.3);
            color: #3730a3;
        }
    </style>
</head>
<body>
    <div class="fade-in">
        <header class="header">
            <div class="header-content">
                <div class="logo">
                    <span class="logo-icon">🌐</span>
                    AI智能翻译
                </div>
                <div class="model-control">
                    <label class="model-label">🤖 翻译模型:</label>
                    <select id="model-select" class="model-select">
                        <option value="">正在加载模型...</option>
                    </select>
                    <button id="refresh-models" class="refresh-btn">
                        <span>🔄</span>
                        刷新模型
                    </button>
                </div>
            </div>
        </header>

        <main class="main-container">
            <div class="translation-workspace">
                <div class="workspace-header">
                    <h1 class="workspace-title">🚀 AI智能翻译</h1>
                    <p class="workspace-subtitle">基于先进的人工智能技术，提供专业的多语言翻译服务</p>
                    <div class="realtime-indicator">
                        <div class="realtime-dot"></div>
                        <span>实时翻译已启用</span>
                    </div>
                </div>

                <div class="language-control">
                    <div class="language-section">
                        <div class="language-label">
                            <span>📝</span>
                            源语言
                        </div>
                        <button class="language-btn active" data-lang="zh">🇨🇳 中文</button>
                    </div>
                    
                    <button class="swap-btn" title="交换语言">
                        ⇄
                    </button>
                    
                    <div class="language-section">
                        <div class="language-label">
                            <span>🎯</span>
                            目标语言
                        </div>
                        <button class="language-btn" data-lang="en">🇺🇸 English</button>
                    </div>
                </div>

                <div class="translation-panels">
                    <div class="text-panel">
                        <div class="panel-header">
                            <div class="panel-title">
                                <span id="source-icon">🇨🇳</span>
                                <span id="source-title">中文输入</span>
                            </div>
                            <div class="char-counter" id="char-count">0 / 5000</div>
                        </div>
                        <textarea id="source-text" class="text-area" 
                                  placeholder="请输入要翻译的文本...支持实时翻译和Ctrl+Enter快速翻译"
                                  maxlength="5000"></textarea>
                        <div class="panel-actions">
                            <button class="action-btn" id="clear-input" title="清空输入">🗑️</button>
                            <button class="action-btn" id="paste-text" title="粘贴文本">📋</button>
                        </div>
                    </div>

                    <div class="text-panel output-panel">
                        <div class="panel-header">
                            <div class="panel-title">
                                <span id="target-icon">🇺🇸</span>
                                <span id="target-title">English翻译</span>
                            </div>
                            <div></div>
                        </div>
                        <textarea id="target-text" class="text-area" 
                                  placeholder="翻译结果将实时显示在这里..." 
                                  readonly></textarea>
                        <div class="loading-overlay" id="loading">
                            <div class="spinner"></div>
                            <span class="loading-text">🤖 AI正在为您翻译中...</span>
                        </div>
                        <div class="panel-actions">
                            <button class="action-btn" id="copy-result" title="复制翻译结果">📋</button>
                            <button class="action-btn" id="clear-output" title="清空输出">🗑️</button>
                        </div>
                    </div>
                </div>

                <div class="controls-section">
                    <div class="realtime-toggle active" id="realtime-toggle">
                        <span>⚡</span>
                        <span>实时翻译</span>
                    </div>
                    <button id="translate-btn" class="translate-btn">
                        <span>✨</span>
                        立即翻译
                        <span>🚀</span>
                    </button>
                </div>
            </div>

            <div id="message" class="message"></div>
        </main>

        <footer class="footer">
            <p>🌟 AI智能翻译工具 - 让语言不再是沟通的障碍 🌟</p>
            <p>💡 支持实时翻译和快捷键：Ctrl+Enter快速翻译</p>
            <p>⚡ 实时翻译可随时开启/关闭</p>
        </footer>
    </div>

    <script>
        // 配置
        const API_BASE = 'http://localhost:8007';
        
        // 全局变量
        let currentSourceLang = 'zh';
        let currentTargetLang = 'en';
        let availableModels = [];
        let realtimeEnabled = true;
        let translateTimeout = null;
        let isTranslating = false;

        // DOM元素
        const elements = {
            modelSelect: document.getElementById('model-select'),
            refreshModels: document.getElementById('refresh-models'),
            sourceText: document.getElementById('source-text'),
            targetText: document.getElementById('target-text'),
            translateBtn: document.getElementById('translate-btn'),
            loading: document.getElementById('loading'),
            charCount: document.getElementById('char-count'),
            sourceTitle: document.getElementById('source-title'),
            targetTitle: document.getElementById('target-title'),
            sourceIcon: document.getElementById('source-icon'),
            targetIcon: document.getElementById('target-icon'),
            message: document.getElementById('message'),
            swapBtn: document.querySelector('.swap-btn'),
            sourceLangBtn: document.querySelector('[data-lang="zh"]'),
            targetLangBtn: document.querySelector('[data-lang="en"]'),
            clearInput: document.getElementById('clear-input'),
            clearOutput: document.getElementById('clear-output'),
            copyResult: document.getElementById('copy-result'),
            pasteText: document.getElementById('paste-text'),
            realtimeToggle: document.getElementById('realtime-toggle')
        };

        // 语言配置
        const langConfig = {
            zh: { name: '🇨🇳 中文', title: '中文输入', icon: '🇨🇳', placeholder: '请输入要翻译的中文文本...' },
            en: { name: '🇺🇸 English', title: 'English输入', icon: '🇺🇸', placeholder: 'Please enter English text to translate...' }
        };

        // 防抖函数
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        // 实时翻译函数
        const realtimeTranslate = debounce(async () => {
            if (!realtimeEnabled || isTranslating) return;
            
            const text = elements.sourceText.value.trim();
            const selectedModel = elements.modelSelect.value;
            
            if (!text || !selectedModel) {
                elements.targetText.value = '';
                return;
            }

            await performTranslation(text, selectedModel, true);
        }, 1200); // 1.2秒防抖

        // 初始化
        async function init() {
            await loadModels();
            bindEvents();
            updateUI();
        }

        // 加载模型列表
        async function loadModels() {
            try {
                showMessage('正在加载模型列表...', 'info');
                const response = await fetch(`${API_BASE}/models`);
                const data = await response.json();
                
                availableModels = data.models || [];
                updateModelSelect();
                
                if (data.error) {
                    showMessage(`模型加载失败: ${data.error}`, 'error');
                } else if (availableModels.length === 0) {
                    showMessage('未获取到任何可用模型，请检查API配置', 'error');
                } else {
                    showMessage(`成功加载 ${availableModels.length} 个模型`, 'success');
                }
            } catch (error) {
                console.error('Failed to load models:', error);
                showMessage('网络错误，无法加载模型列表', 'error');
                availableModels = [];
                updateModelSelect();
            }
        }

        // 更新模型选择器
        function updateModelSelect() {
            elements.modelSelect.innerHTML = '';
            
            if (availableModels.length === 0) {
                elements.modelSelect.innerHTML = '<option value="">无可用模型 - 请刷新重试</option>';
                elements.modelSelect.disabled = true;
                return;
            }

            // 添加所有获取到的模型
            availableModels.forEach(model => {
                const option = document.createElement('option');
                option.value = model.id;
                option.textContent = model.id;  // 直接显示模型ID
                elements.modelSelect.appendChild(option);
            });

            // 启用选择器并选择第一个模型
            elements.modelSelect.disabled = false;
            if (availableModels.length > 0) {
                elements.modelSelect.value = availableModels[0].id;
            }
        }

        // 绑定事件
        function bindEvents() {
            // 模型相关
            elements.refreshModels.addEventListener('click', loadModels);

            // 翻译相关
            elements.translateBtn.addEventListener('click', handleTranslate);
            elements.sourceText.addEventListener('input', (e) => {
                updateCharCount();
                if (realtimeEnabled) {
                    realtimeTranslate();
                }
            });
            elements.sourceText.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' && e.ctrlKey) {
                    e.preventDefault();
                    handleTranslate();
                }
            });

            // 实时翻译开关
            elements.realtimeToggle.addEventListener('click', toggleRealtime);

            // 语言切换
            elements.swapBtn.addEventListener('click', swapLanguages);

            // 操作按钮
            elements.clearInput.addEventListener('click', () => {
                elements.sourceText.value = '';
                elements.targetText.value = '';
                updateCharCount();
                hideMessage();
            });

            elements.clearOutput.addEventListener('click', () => {
                elements.targetText.value = '';
                hideMessage();
            });

            elements.copyResult.addEventListener('click', copyResult);
            elements.pasteText.addEventListener('click', pasteText);
        }

        // 切换实时翻译
        function toggleRealtime() {
            realtimeEnabled = !realtimeEnabled;
            elements.realtimeToggle.classList.toggle('active', realtimeEnabled);
            
            const toggleText = elements.realtimeToggle.querySelector('span:last-child');
            toggleText.textContent = realtimeEnabled ? '实时翻译' : '手动翻译';
            
            // 更新指示器
            const indicator = document.querySelector('.realtime-indicator span');
            indicator.textContent = realtimeEnabled ? '实时翻译已启用' : '实时翻译已关闭';
            
            showMessage(realtimeEnabled ? '✅ 实时翻译已启用' : '⏸️ 实时翻译已关闭', 'info');
        }

        // 执行翻译的核心函数
        async function performTranslation(text, selectedModel, isRealtime = false) {
            if (isTranslating && isRealtime) return; // 避免实时翻译冲突
            
            isTranslating = true;
            
            if (!isRealtime) {
                setLoading(true);
                hideMessage();
            } else {
                // 实时翻译时显示顶部进度条
                elements.targetText.parentElement.classList.add('translating');
            }

            try {
                const response = await fetch(`${API_BASE}/translate`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        text: text,
                        source_lang: currentSourceLang,
                        target_lang: currentTargetLang,
                        model: selectedModel
                    })
                });

                const data = await response.json();

                if (response.ok) {
                    elements.targetText.value = data.translated_text;
                    if (!isRealtime) {
                        showMessage('✅ 翻译完成!', 'success');
                    }
                } else {
                    throw new Error(data.detail || '翻译失败');
                }
            } catch (error) {
                console.error('Translation error:', error);
                if (!isRealtime) {
                    showMessage(error.message || '翻译服务暂时不可用，请稍后重试', 'error');
                }
            } finally {
                isTranslating = false;
                if (!isRealtime) {
                    setLoading(false);
                } else {
                    elements.targetText.parentElement.classList.remove('translating');
                }
            }
        }

        // 手动翻译
        async function handleTranslate() {
            const text = elements.sourceText.value.trim();
            const selectedModel = elements.modelSelect.value;
            
            if (!text) {
                showMessage('请输入要翻译的文本', 'error');
                return;
            }

            if (!selectedModel) {
                showMessage('请选择翻译模型', 'error');
                return;
            }

            await performTranslation(text, selectedModel, false);
        }

        // 交换语言
        function swapLanguages() {
            [currentSourceLang, currentTargetLang] = [currentTargetLang, currentSourceLang];
            
            // 交换文本
            const temp = elements.sourceText.value;
            elements.sourceText.value = elements.targetText.value;
            elements.targetText.value = temp;
            
            updateUI();
            updateCharCount();
            hideMessage();
            
            // 如果开启实时翻译且有文本，立即翻译
            if (realtimeEnabled && elements.sourceText.value.trim()) {
                realtimeTranslate();
            }
        }

        // 更新UI
        function updateUI() {
            const sourceConfig = langConfig[currentSourceLang];
            const targetConfig = langConfig[currentTargetLang];
            
            elements.sourceTitle.textContent = sourceConfig.title;
            elements.targetTitle.textContent = targetConfig.title;
            elements.sourceIcon.textContent = sourceConfig.icon;
            elements.targetIcon.textContent = targetConfig.icon;
            
            elements.sourceLangBtn.textContent = sourceConfig.name;
            elements.targetLangBtn.textContent = targetConfig.name;
            
            elements.sourceText.placeholder = sourceConfig.placeholder + '支持实时翻译和Ctrl+Enter快速翻译';
            elements.targetText.placeholder = `${targetConfig.name}翻译结果将${realtimeEnabled ? '实时' : ''}显示在这里...`;
            
            // 更新按钮状态
            elements.sourceLangBtn.classList.toggle('active', true);
            elements.targetLangBtn.classList.toggle('active', true);
        }

        // 更新字符计数
        function updateCharCount() {
            const count = elements.sourceText.value.length;
            elements.charCount.textContent = `${count} / 5000`;
            
            if (count > 4500) {
                elements.charCount.style.color = '#dc2626';
            } else if (count > 4000) {
                elements.charCount.style.color = '#d97706';
            } else {
                elements.charCount.style.color = '#64748b';
            }
        }

        // 复制结果
        async function copyResult() {
            const text = elements.targetText.value.trim();
            
            if (!text) {
                showMessage('没有可复制的内容', 'error');
                return;
            }

            try {
                await navigator.clipboard.writeText(text);
                showMessage('📋 复制成功！', 'success');
            } catch (error) {
                // 降级方案
                elements.targetText.select();
                try {
                    document.execCommand('copy');
                    showMessage('📋 复制成功！', 'success');
                } catch (e) {
                    showMessage('复制失败，请手动选择复制', 'error');
                }
            }
        }

        // 粘贴文本
        async function pasteText() {
            try {
                const text = await navigator.clipboard.readText();
                if (text) {
                    elements.sourceText.value = text.substring(0, 5000);
                    updateCharCount();
                    showMessage('📋 粘贴成功！', 'success');
                    
                    // 如果开启实时翻译，立即翻译
                    if (realtimeEnabled) {
                        realtimeTranslate();
                    }
                } else {
                    showMessage('剪贴板为空', 'error');
                }
            } catch (error) {
                showMessage('无法访问剪贴板，请手动粘贴', 'error');
            }
        }

        // 设置加载状态
        function setLoading(loading) {
            if (loading) {
                elements.loading.classList.add('show');
                elements.translateBtn.disabled = true;
                elements.translateBtn.innerHTML = '<span>⏳</span> 翻译中... <span>🤖</span>';
            } else {
                elements.loading.classList.remove('show');
                elements.translateBtn.disabled = false;
                elements.translateBtn.innerHTML = '<span>✨</span> 立即翻译 <span>🚀</span>';
            }
        }

        // 显示消息
        function showMessage(text, type = 'info') {
            const icons = {
                success: '✅',
                error: '❌',
                info: 'ℹ️'
            };
            
            elements.message.innerHTML = `${icons[type]} ${text}`;
            elements.message.className = `message show ${type}`;
            
            if (type === 'success' || type === 'info') {
                setTimeout(hideMessage, 4000);
            }
        }

        // 隐藏消息
        function hideMessage() {
            elements.message.classList.remove('show');
        }

        // 启动应用
        document.addEventListener('DOMContentLoaded', init);
    </script>
</body>
</html> 