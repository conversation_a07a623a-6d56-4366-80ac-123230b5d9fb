<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI智能翻译</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🔄</text></svg>">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-color: #4f46e5;
            --primary-hover: #4338ca;
            --secondary-color: #6b7280;
            --success-color: #10b981;
            --error-color: #ef4444;
            
            --bg-primary: #ffffff;
            --bg-secondary: #f8fafc;
            --bg-tertiary: #f1f5f9;
            
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --text-muted: #94a3b8;
            
            --border-color: #e2e8f0;
            --border-hover: #cbd5e1;
            --border-focus: #4f46e5;
            
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            
            --border-radius: 8px;
            --border-radius-lg: 12px;
            --border-radius-xl: 16px;
            
            --transition: all 0.2s ease-in-out;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: var(--bg-secondary);
            color: var(--text-primary);
            line-height: 1.6;
            min-height: 100vh;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 1rem;
        }

        .header {
            text-align: center;
            padding: 1.5rem 0;
            margin-bottom: 1rem;
        }

        .logo {
            font-size: 1.8rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .logo::before {
            content: '🔄';
            font-size: 1.5rem;
        }

        .subtitle {
            font-size: 1rem;
            color: var(--text-secondary);
            margin-bottom: 1rem;
        }

        .main-card {
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-lg);
            overflow: hidden;
        }

        .language-control {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 1.5rem;
            border-bottom: 1px solid var(--border-color);
            gap: 2rem;
            background: var(--bg-tertiary);
        }

        .language-btn {
            padding: 0.8rem 2rem;
            border: 2px solid var(--border-color);
            border-radius: 50px;
            background: var(--bg-primary);
            color: var(--text-primary);
            font-weight: 600;
            font-size: 1rem;
            cursor: pointer;
            transition: var(--transition);
            min-width: 120px;
        }

        .language-btn:hover {
            border-color: var(--primary-color);
            background: var(--primary-color);
            color: white;
            transform: translateY(-2px);
        }

        .swap-btn {
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            font-size: 1.2rem;
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .swap-btn:hover {
            background: var(--primary-hover);
            transform: rotate(180deg) scale(1.1);
        }

        .translation-panels {
            display: grid;
            grid-template-columns: 1fr 1fr;
            min-height: 500px;
        }

        .text-panel {
            position: relative;
            display: flex;
            flex-direction: column;
        }

        .text-panel:first-child {
            border-right: 1px solid var(--border-color);
        }

        .panel-header {
            padding: 1rem 1.5rem;
            background: var(--bg-tertiary);
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .panel-title {
            font-weight: 600;
            color: var(--text-primary);
            font-size: 0.9rem;
        }

        .char-counter {
            font-size: 0.8rem;
            color: var(--text-muted);
        }

        .text-area {
            flex: 1;
            padding: 1.5rem;
            border: none;
            resize: none;
            font-size: 1rem;
            line-height: 1.6;
            background: transparent;
            color: var(--text-primary);
            outline: none;
            font-family: inherit;
            min-height: 400px;
        }

        .text-area::placeholder {
            color: var(--text-muted);
            font-style: italic;
        }

        .text-area:focus {
            background: rgba(79, 70, 229, 0.02);
        }

        .output-panel {
            background: var(--bg-secondary);
        }

        .output-panel .text-area {
            background: transparent;
            color: var(--text-secondary);
        }

        .panel-actions {
            position: absolute;
            bottom: 1rem;
            right: 1rem;
            display: flex;
            gap: 0.5rem;
        }

        .action-btn {
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: 0.5rem;
            cursor: pointer;
            transition: var(--transition);
            font-size: 1rem;
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: var(--shadow-sm);
        }

        .action-btn:hover {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
            transform: translateY(-1px);
        }

        .control-panel {
            padding: 1.5rem;
            text-align: center;
            border-top: 1px solid var(--border-color);
            background: var(--bg-tertiary);
        }

        .translate-btn {
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 50px;
            padding: 1rem 3rem;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            box-shadow: var(--shadow-md);
            margin-right: 1rem;
        }

        .translate-btn:hover {
            background: var(--primary-hover);
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .translate-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .secondary-btn {
            background: transparent;
            color: var(--text-secondary);
            border: 2px solid var(--border-color);
            border-radius: 50px;
            padding: 1rem 2rem;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
        }

        .secondary-btn:hover {
            border-color: var(--primary-color);
            color: var(--primary-color);
            background: rgba(79, 70, 229, 0.05);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .translation-panels {
                grid-template-columns: 1fr;
            }
            
            .text-panel:first-child {
                border-right: none;
                border-bottom: 1px solid var(--border-color);
            }
            
            .language-control {
                flex-direction: column;
                gap: 1rem;
            }
            
            .translate-btn, .secondary-btn {
                width: 100%;
                margin: 0.5rem 0;
            }
        }

        /* 加载状态 */
        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            visibility: hidden;
            transition: var(--transition);
        }

        .loading-overlay.active {
            opacity: 1;
            visibility: visible;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid var(--border-color);
            border-top: 3px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Toast 通知 */
        .toast {
            position: fixed;
            top: 2rem;
            right: 2rem;
            padding: 1rem 1.5rem;
            border-radius: var(--border-radius);
            color: white;
            font-weight: 600;
            z-index: 2000;
            transform: translateX(100%);
            transition: var(--transition);
            box-shadow: var(--shadow-lg);
        }

        .toast.show {
            transform: translateX(0);
        }

        .toast.success {
            background: var(--success-color);
        }

        .toast.error {
            background: var(--error-color);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">AI智能翻译</div>
            <div class="subtitle">基于先进的人工智能技术，提供专业多语言翻译服务</div>
        </div>

        <div class="main-card">
            <div class="language-control">
                <button class="language-btn" id="sourceLang">中文</button>
                <button class="swap-btn" id="swapBtn" title="交换语言">⇄</button>
                <button class="language-btn" id="targetLang">English</button>
            </div>

            <div class="translation-panels">
                <div class="text-panel">
                    <div class="panel-header">
                        <div class="panel-title">输入文本</div>
                        <div class="char-counter" id="inputCounter">0 / 5000</div>
                    </div>
                    <textarea 
                        class="text-area" 
                        id="inputText" 
                        placeholder="请输入要翻译的文本..."
                        maxlength="5000"
                    ></textarea>
                    <div class="panel-actions">
                        <button class="action-btn" id="clearInput" title="清空">🗑️</button>
                        <button class="action-btn" id="pasteBtn" title="粘贴">📋</button>
                    </div>
                </div>

                <div class="text-panel output-panel">
                    <div class="panel-header">
                        <div class="panel-title">翻译结果</div>
                        <div class="char-counter" id="outputCounter">0 字符</div>
                    </div>
                    <textarea 
                        class="text-area" 
                        id="outputText" 
                        placeholder="翻译结果将在这里显示..."
                        readonly
                    ></textarea>
                    <div class="panel-actions">
                        <button class="action-btn" id="copyOutput" title="复制">📄</button>
                        <button class="action-btn" id="downloadBtn" title="下载">💾</button>
                    </div>
                </div>
            </div>

            <div class="control-panel">
                <button class="translate-btn" id="translateBtn">立即翻译</button>
                <button class="secondary-btn" id="clearAllBtn">清空全部</button>
            </div>

            <div class="loading-overlay" id="loadingOverlay">
                <div class="loading-spinner"></div>
            </div>
        </div>
    </div>

    <div class="toast" id="toast"></div>

    <script>
        // 配置
        const API_BASE = 'http://localhost:8009';

        // 全局变量
        let currentSourceLang = 'zh';
        let currentTargetLang = 'en';
        let isTranslating = false;

        // DOM元素
        const elements = {
            inputText: document.getElementById('inputText'),
            outputText: document.getElementById('outputText'),
            translateBtn: document.getElementById('translateBtn'),
            loadingOverlay: document.getElementById('loadingOverlay'),
            inputCounter: document.getElementById('inputCounter'),
            outputCounter: document.getElementById('outputCounter'),
            toast: document.getElementById('toast'),
            swapBtn: document.getElementById('swapBtn'),
            sourceLang: document.getElementById('sourceLang'),
            targetLang: document.getElementById('targetLang'),
            clearInput: document.getElementById('clearInput'),
            clearAllBtn: document.getElementById('clearAllBtn'),
            copyOutput: document.getElementById('copyOutput'),
            pasteBtn: document.getElementById('pasteBtn'),
            downloadBtn: document.getElementById('downloadBtn')
        };

        // 语言配置
        const langConfig = {
            zh: { name: '中文', placeholder: '请输入要翻译的中文文本...' },
            en: { name: 'English', placeholder: 'Please enter English text to translate...' },
            ja: { name: '日本語', placeholder: '翻訳したい日本語のテキストを入力してください...' },
            ko: { name: '한국어', placeholder: '번역할 한국어 텍스트를 입력하세요...' },
            fr: { name: 'Français', placeholder: 'Veuillez saisir le texte français à traduire...' },
            de: { name: 'Deutsch', placeholder: 'Bitte geben Sie den zu übersetzenden deutschen Text ein...' },
            es: { name: 'Español', placeholder: 'Por favor, ingrese el texto en español para traducir...' },
            it: { name: 'Italiano', placeholder: 'Inserisci il testo italiano da tradurre...' },
            pt: { name: 'Português', placeholder: 'Digite o texto em português para traduzir...' },
            ru: { name: 'Русский', placeholder: 'Введите русский текст для перевода...' },
            ar: { name: 'العربية', placeholder: 'يرجى إدخال النص العربي للترجمة...' },
            hi: { name: 'हिन्दी', placeholder: 'कृपया अनुवाद के लिए हिंदी पाठ दर्ज करें...' },
            th: { name: 'ไทย', placeholder: 'กรุณาป้อนข้อความภาษาไทยเพื่อแปล...' },
            vi: { name: 'Tiếng Việt', placeholder: 'Vui lòng nhập văn bản tiếng Việt để dịch...' }
        };

        // 初始化
        function init() {
            bindEvents();
            updateUI();
            updateCharCount();
        }

        // 绑定事件
        function bindEvents() {
            // 翻译相关
            elements.translateBtn.addEventListener('click', handleTranslate);
            elements.inputText.addEventListener('input', updateCharCount);
            elements.inputText.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' && e.ctrlKey) {
                    e.preventDefault();
                    handleTranslate();
                }
            });

            // 语言切换
            elements.swapBtn.addEventListener('click', swapLanguages);
            elements.sourceLang.addEventListener('click', () => showLanguageSelector('source'));
            elements.targetLang.addEventListener('click', () => showLanguageSelector('target'));

            // 操作按钮
            elements.clearInput.addEventListener('click', () => {
                elements.inputText.value = '';
                elements.outputText.value = '';
                updateCharCount();
                updateOutputCounter();
            });

            elements.clearAllBtn.addEventListener('click', () => {
                elements.inputText.value = '';
                elements.outputText.value = '';
                updateCharCount();
                updateOutputCounter();
                hideToast();
            });

            elements.copyOutput.addEventListener('click', copyResult);
            elements.pasteBtn.addEventListener('click', pasteText);
            elements.downloadBtn.addEventListener('click', downloadResult);
        }

        // 执行翻译
        async function handleTranslate() {
            const text = elements.inputText.value.trim();

            if (!text) {
                showToast('请输入要翻译的文本', 'error');
                return;
            }

            if (isTranslating) {
                return;
            }

            isTranslating = true;
            setLoading(true);
            hideToast();

            try {
                const response = await fetch(`${API_BASE}/translate`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        text: text,
                        source_lang: currentSourceLang,
                        target_lang: currentTargetLang,
                        model: 'gpt-3.5-turbo'
                    })
                });

                const data = await response.json();

                if (response.ok) {
                    elements.outputText.value = data.translated_text;
                    updateOutputCounter();
                    showToast('翻译完成！', 'success');
                } else {
                    throw new Error(data.detail || '翻译失败');
                }
            } catch (error) {
                console.error('Translation error:', error);
                showToast(error.message || '翻译服务暂时不可用，请稍后重试', 'error');
            } finally {
                isTranslating = false;
                setLoading(false);
            }
        }

        // 交换语言
        function swapLanguages() {
            [currentSourceLang, currentTargetLang] = [currentTargetLang, currentSourceLang];

            // 交换文本
            const temp = elements.inputText.value;
            elements.inputText.value = elements.outputText.value;
            elements.outputText.value = temp;

            updateUI();
            updateCharCount();
            updateOutputCounter();
            hideToast();
        }

        // 显示语言选择器
        function showLanguageSelector(type) {
            const languages = Object.keys(langConfig);
            const currentLang = type === 'source' ? currentSourceLang : currentTargetLang;

            const selector = document.createElement('div');
            selector.className = 'language-selector';
            selector.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: white;
                border: 1px solid var(--border-color);
                border-radius: var(--border-radius-lg);
                box-shadow: var(--shadow-lg);
                z-index: 1000;
                max-height: 400px;
                overflow-y: auto;
                min-width: 200px;
            `;

            const overlay = document.createElement('div');
            overlay.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.5);
                z-index: 999;
            `;

            languages.forEach(lang => {
                const option = document.createElement('div');
                option.textContent = langConfig[lang].name;
                option.style.cssText = `
                    padding: 0.75rem 1rem;
                    cursor: pointer;
                    border-bottom: 1px solid var(--border-color);
                    transition: var(--transition);
                    ${lang === currentLang ? 'background: var(--primary-color); color: white;' : ''}
                `;

                option.addEventListener('mouseenter', () => {
                    if (lang !== currentLang) {
                        option.style.background = 'var(--bg-tertiary)';
                    }
                });

                option.addEventListener('mouseleave', () => {
                    if (lang !== currentLang) {
                        option.style.background = '';
                    }
                });

                option.addEventListener('click', () => {
                    if (type === 'source') {
                        currentSourceLang = lang;
                    } else {
                        currentTargetLang = lang;
                    }
                    updateUI();
                    document.body.removeChild(overlay);
                    document.body.removeChild(selector);
                });

                selector.appendChild(option);
            });

            overlay.addEventListener('click', () => {
                document.body.removeChild(overlay);
                document.body.removeChild(selector);
            });

            document.body.appendChild(overlay);
            document.body.appendChild(selector);
        }

        // 更新UI
        function updateUI() {
            const sourceConfig = langConfig[currentSourceLang];
            const targetConfig = langConfig[currentTargetLang];

            elements.sourceLang.textContent = sourceConfig.name;
            elements.targetLang.textContent = targetConfig.name;

            elements.inputText.placeholder = sourceConfig.placeholder;
            elements.outputText.placeholder = `${targetConfig.name}翻译结果将显示在这里...`;
        }

        // 更新字符计数
        function updateCharCount() {
            const count = elements.inputText.value.length;
            elements.inputCounter.textContent = `${count} / 5000`;

            if (count > 4500) {
                elements.inputCounter.style.color = '#dc2626';
            } else if (count > 4000) {
                elements.inputCounter.style.color = '#d97706';
            } else {
                elements.inputCounter.style.color = '#64748b';
            }
        }

        // 更新输出计数
        function updateOutputCounter() {
            const count = elements.outputText.value.length;
            elements.outputCounter.textContent = `${count} 字符`;
        }

        // 复制结果
        async function copyResult() {
            const text = elements.outputText.value.trim();

            if (!text) {
                showToast('没有可复制的内容', 'error');
                return;
            }

            try {
                await navigator.clipboard.writeText(text);
                showToast('复制成功！', 'success');
            } catch (error) {
                // 降级方案
                elements.outputText.select();
                try {
                    document.execCommand('copy');
                    showToast('复制成功！', 'success');
                } catch (e) {
                    showToast('复制失败，请手动选择复制', 'error');
                }
            }
        }

        // 粘贴文本
        async function pasteText() {
            try {
                const text = await navigator.clipboard.readText();
                if (text) {
                    elements.inputText.value = text.substring(0, 5000);
                    updateCharCount();
                    showToast('粘贴成功！', 'success');
                } else {
                    showToast('剪贴板为空', 'error');
                }
            } catch (error) {
                showToast('无法访问剪贴板，请手动粘贴', 'error');
            }
        }

        // 下载结果
        function downloadResult() {
            const text = elements.outputText.value.trim();

            if (!text) {
                showToast('没有可下载的内容', 'error');
                return;
            }

            const blob = new Blob([text], { type: 'text/plain;charset=utf-8' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `translation_${new Date().toISOString().slice(0, 10)}.txt`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            showToast('下载成功！', 'success');
        }

        // 设置加载状态
        function setLoading(loading) {
            if (loading) {
                elements.loadingOverlay.classList.add('active');
                elements.translateBtn.disabled = true;
                elements.translateBtn.textContent = '翻译中...';
            } else {
                elements.loadingOverlay.classList.remove('active');
                elements.translateBtn.disabled = false;
                elements.translateBtn.textContent = '立即翻译';
            }
        }

        // 显示Toast
        function showToast(message, type = 'info') {
            elements.toast.textContent = message;
            elements.toast.className = `toast show ${type}`;

            setTimeout(() => {
                hideToast();
            }, 3000);
        }

        // 隐藏Toast
        function hideToast() {
            elements.toast.classList.remove('show');
        }

        // 启动应用
        document.addEventListener('DOMContentLoaded', init);
    </script>
</body>
</html>
