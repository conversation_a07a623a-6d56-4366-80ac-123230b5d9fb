<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI智能翻译</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🔄</text></svg>">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-color: #4f46e5;
            --primary-hover: #4338ca;
            --secondary-color: #6b7280;
            --success-color: #10b981;
            --error-color: #ef4444;
            
            --bg-primary: #ffffff;
            --bg-secondary: #f8fafc;
            --bg-tertiary: #f1f5f9;
            
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --text-muted: #94a3b8;
            
            --border-color: #e2e8f0;
            --border-hover: #cbd5e1;
            --border-focus: #4f46e5;
            
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            
            --border-radius: 8px;
            --border-radius-lg: 12px;
            --border-radius-xl: 16px;
            
            --transition: all 0.2s ease-in-out;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: var(--bg-secondary);
            color: var(--text-primary);
            line-height: 1.6;
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 1rem;
        }

        .header {
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-md);
            padding: 1.5rem 2rem;
            margin-bottom: 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .logo-section {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .logo {
            font-size: 1.8rem;
            font-weight: 700;
            color: var(--primary-color);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .logo::before {
            content: '🌐';
            font-size: 1.5rem;
        }

        .subtitle {
            font-size: 0.9rem;
            color: var(--text-secondary);
            margin-left: 0.5rem;
        }

        .model-control {
            display: flex;
            align-items: center;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .model-label {
            font-size: 0.9rem;
            color: var(--text-secondary);
            font-weight: 600;
        }

        .model-select {
            padding: 0.75rem 1rem;
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius);
            background: var(--bg-primary);
            font-size: 0.9rem;
            color: var(--text-primary);
            min-width: 200px;
            transition: var(--transition);
            cursor: pointer;
        }

        .model-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
        }



        .refresh-btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: var(--border-radius);
            background: var(--primary-color);
            color: white;
            font-weight: 600;
            font-size: 0.9rem;
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .refresh-btn:hover {
            background: var(--primary-hover);
            transform: translateY(-1px);
        }

        .realtime-toggle {
            background: transparent;
            color: var(--text-secondary);
            border: 2px solid var(--border-color);
            border-radius: 50px;
            padding: 0.5rem 1rem;
            font-size: 0.85rem;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .realtime-toggle.active {
            background: var(--success-color);
            border-color: var(--success-color);
            color: white;
        }

        .realtime-toggle:hover {
            transform: translateY(-1px);
        }

        .main-card {
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-lg);
            overflow: hidden;
        }

        .language-control {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 1.5rem;
            border-bottom: 1px solid var(--border-color);
            gap: 2rem;
            background: var(--bg-tertiary);
        }

        .language-btn {
            padding: 0.8rem 2rem;
            border: 2px solid var(--border-color);
            border-radius: 50px;
            background: var(--bg-primary);
            color: var(--text-primary);
            font-weight: 600;
            font-size: 1rem;
            cursor: pointer;
            transition: var(--transition);
            min-width: 120px;
        }

        .language-btn:hover {
            border-color: var(--primary-color);
            background: var(--primary-color);
            color: white;
            transform: translateY(-2px);
        }

        .swap-btn {
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            font-size: 1.2rem;
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .swap-btn:hover {
            background: var(--primary-hover);
            transform: rotate(180deg) scale(1.1);
        }

        .translation-panels {
            display: grid;
            grid-template-columns: 1fr 1fr;
            min-height: 600px;
        }

        .text-panel {
            position: relative;
            display: flex;
            flex-direction: column;
        }

        .text-panel:first-child {
            border-right: 1px solid var(--border-color);
        }

        .panel-header {
            padding: 1rem 1.5rem;
            background: var(--bg-tertiary);
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .panel-title {
            font-weight: 600;
            color: var(--text-primary);
            font-size: 0.9rem;
        }

        .char-counter {
            font-size: 0.8rem;
            color: var(--text-muted);
        }

        .text-area {
            flex: 1;
            padding: 2rem;
            border: none;
            resize: none;
            font-size: 1.1rem;
            line-height: 1.7;
            background: transparent;
            color: var(--text-primary);
            outline: none;
            font-family: inherit;
            min-height: 500px;
            /* 优化渲染性能，减少重绘和抖动 */
            will-change: scroll-position;
            transform: translateZ(0);
            backface-visibility: hidden;
            contain: layout style paint;
        }

        .text-area::placeholder {
            color: var(--text-muted);
            font-style: italic;
        }

        .text-area:focus {
            background: rgba(79, 70, 229, 0.02);
        }

        .output-panel {
            background: var(--bg-secondary);
        }

        .output-panel .text-area {
            background: transparent;
            color: var(--text-secondary);
        }

        .panel-actions {
            position: absolute;
            bottom: 1rem;
            right: 1rem;
            display: flex;
            gap: 0.5rem;
            align-items: center;
        }

        .shortcut-hint {
            font-size: 0.8rem;
            color: var(--text-secondary);
            display: flex;
            align-items: center;
            gap: 0.25rem;
            padding: 0.25rem 0.5rem;
            background: var(--bg-secondary);
            border-radius: 4px;
            border: 1px solid var(--border-color);
            position: absolute;
            bottom: 4rem;
            left: 0;
            white-space: nowrap;
        }

        .shortcut-hint kbd {
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            border-radius: 3px;
            padding: 0.1rem 0.3rem;
            font-size: 0.75rem;
            font-family: monospace;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .action-btn {
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: 0.5rem;
            cursor: pointer;
            transition: var(--transition);
            font-size: 1rem;
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: var(--shadow-sm);
        }

        .action-btn:hover {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
            transform: translateY(-1px);
        }

        .control-panel {
            padding: 1.5rem;
            text-align: center;
            border-top: 1px solid var(--border-color);
            background: var(--bg-tertiary);
        }

        .translate-btn {
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 50px;
            padding: 1rem 3rem;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            box-shadow: var(--shadow-md);
            margin-right: 1rem;
        }

        .translate-btn:hover {
            background: var(--primary-hover);
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .translate-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .secondary-btn {
            background: transparent;
            color: var(--text-secondary);
            border: 2px solid var(--border-color);
            border-radius: 50px;
            padding: 1rem 2rem;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
        }

        .secondary-btn:hover {
            border-color: var(--primary-color);
            color: var(--primary-color);
            background: rgba(79, 70, 229, 0.05);
        }

        /* 状态指示器 */
        .status-indicator {
            position: fixed;
            top: 1rem;
            right: 1rem;
            padding: 0.5rem 1rem;
            border-radius: 50px;
            font-size: 0.8rem;
            font-weight: 600;
            z-index: 1000;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            background: var(--success-color);
            color: white;
            box-shadow: var(--shadow-md);
            opacity: 0;
            transform: translateX(100%);
            transition: var(--transition);
        }

        .status-indicator.show {
            opacity: 1;
            transform: translateX(0);
        }

        .status-indicator.realtime::before {
            content: '⚡';
            animation: pulse 2s infinite;
        }

        .status-indicator.manual {
            background: var(--secondary-color);
        }

        .status-indicator.manual::before {
            content: '⏸️';
        }

        .status-indicator.preparing {
            background: #f59e0b;
        }

        .status-indicator.preparing::before {
            content: '⏳';
            animation: pulse 1s infinite;
        }

        .status-indicator.translating {
            background: var(--primary-color);
        }

        .status-indicator.translating::before {
            content: '🔄';
            animation: spin 1s linear infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .header {
                flex-direction: column;
                text-align: center;
            }

            .model-control {
                justify-content: center;
                width: 100%;
            }

            .model-select {
                min-width: auto;
                width: 100%;
                max-width: 300px;
            }

            .translation-panels {
                grid-template-columns: 1fr;
            }

            .text-panel:first-child {
                border-right: none;
                border-bottom: 1px solid var(--border-color);
            }

            .language-control {
                flex-direction: column;
                gap: 1rem;
            }

            .translate-btn, .secondary-btn {
                width: 100%;
                margin: 0.5rem 0;
            }

            .text-area {
                padding: 1.5rem;
                font-size: 1rem;
                min-height: 300px;
            }
        }

        /* 加载状态 */
        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            visibility: hidden;
            transition: var(--transition);
        }

        .loading-overlay.active {
            opacity: 1;
            visibility: visible;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid var(--border-color);
            border-top: 3px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 流式翻译指示器 */
        .streaming-indicator {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-color), var(--success-color));
            background-size: 200% 100%;
            animation: streamingFlow 2s linear infinite;
            opacity: 0;
            transition: var(--transition);
        }

        .streaming-indicator.active {
            opacity: 1;
        }

        @keyframes streamingFlow {
            0% { background-position: -200% 0; }
            100% { background-position: 200% 0; }
        }

        /* 快捷键反馈指示器 */
        .shortcut-feedback {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #10b981, #3b82f6, #8b5cf6);
            transform: scaleX(0);
            transform-origin: left;
            transition: transform 0.3s ease;
            z-index: 15;
            border-radius: 0 0 2px 2px;
        }

        .shortcut-feedback.active {
            transform: scaleX(1);
            animation: translatingPulse 2s ease-in-out infinite;
        }

        @keyframes translatingPulse {
            0% {
                opacity: 0.8;
                background: linear-gradient(90deg, #10b981, #3b82f6, #8b5cf6);
            }
            50% {
                opacity: 1;
                background: linear-gradient(90deg, #8b5cf6, #10b981, #3b82f6);
            }
            100% {
                opacity: 0.8;
                background: linear-gradient(90deg, #3b82f6, #8b5cf6, #10b981);
            }
        }

        /* 翻译状态提示 */
        .translation-status {
            position: absolute;
            top: 8px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(59, 130, 246, 0.95);
            color: white;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 500;
            z-index: 20;
            opacity: 0;
            transition: opacity 0.3s ease;
            backdrop-filter: blur(4px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .translation-status.show {
            opacity: 1;
        }

        /* 打字机效果 */
        .typing-cursor {
            display: inline-block;
            width: 2px;
            height: 1.2em;
            background: var(--primary-color);
            animation: blink 1s infinite;
            margin-left: 2px;
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
        }

        /* Toast 通知 */
        .toast {
            position: fixed;
            top: 2rem;
            right: 2rem;
            padding: 1rem 1.5rem;
            border-radius: var(--border-radius);
            color: white;
            font-weight: 600;
            z-index: 2000;
            transform: translateX(100%);
            transition: var(--transition);
            box-shadow: var(--shadow-lg);
        }

        .toast.show {
            transform: translateX(0);
        }

        .toast.success {
            background: var(--success-color);
        }

        .toast.error {
            background: var(--error-color);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo-section">
                <div class="logo">AI智能翻译</div>
                <div class="subtitle">基于先进的人工智能技术，提供专业多语言翻译服务</div>
            </div>
            <div class="model-control">
                <span class="model-label">🤖 翻译模型:</span>
                <select class="model-select" id="modelSelect">
                    <option value="">正在加载模型...</option>
                </select>
                <button class="refresh-btn" id="refreshBtn">
                    <span>🔄</span>
                    刷新模型
                </button>
                <button class="realtime-toggle active" id="realtimeToggle">
                    <span>⚡</span>
                    <span>实时翻译</span>
                </button>
                <button class="realtime-toggle" id="strictModeToggle" title="严格翻译模式：强制模型只翻译，不回答问题">
                    <span>🔒</span>
                    <span>严格模式</span>
                </button>
            </div>
        </div>

        <div class="main-card">
            <div class="language-control">
                <button class="language-btn" id="sourceLang">中文</button>
                <button class="swap-btn" id="swapBtn" title="交换语言">⇄</button>
                <button class="language-btn" id="targetLang">English</button>
            </div>

            <div class="translation-panels">
                <div class="text-panel">
                    <div class="shortcut-feedback" id="shortcutFeedback"></div>
                    <div class="translation-status" id="translationStatus">正在翻译，请勿重复操作...</div>
                    <div class="panel-header">
                        <div class="panel-title">输入文本</div>
                        <div class="char-counter" id="inputCounter">0 / 1000000</div>
                    </div>
                    <textarea
                        class="text-area"
                        id="inputText"
                        placeholder="请输入要翻译的文本... (按 Ctrl+Enter 翻译)"
                        maxlength="1000000"
                    ></textarea>
                    <div class="panel-actions">
                        <div class="shortcut-hint">⚡ 按 <kbd>Ctrl</kbd> + <kbd>Enter</kbd> 翻译</div>
                        <button class="action-btn" id="clearInput" title="清空">🗑️</button>
                        <button class="action-btn" id="pasteBtn" title="粘贴">📋</button>
                    </div>
                </div>

                <div class="text-panel output-panel">
                    <div class="streaming-indicator" id="streamingIndicator"></div>
                    <div class="panel-header">
                        <div class="panel-title">翻译结果</div>
                        <div class="char-counter" id="outputCounter">0 字符</div>
                    </div>
                    <textarea
                        class="text-area"
                        id="outputText"
                        placeholder="翻译结果将在这里显示..."
                        readonly
                    ></textarea>
                    <div class="panel-actions">
                        <button class="action-btn" id="copyOutput" title="复制">📄</button>
                        <button class="action-btn" id="downloadBtn" title="下载">💾</button>
                    </div>
                </div>
            </div>

            <div class="control-panel">
                <button class="translate-btn" id="translateBtn">立即翻译</button>
                <button class="secondary-btn" id="clearAllBtn">清空全部</button>
            </div>

            <div class="loading-overlay" id="loadingOverlay">
                <div class="loading-spinner"></div>
            </div>
        </div>
    </div>

    <div class="toast" id="toast"></div>
    <div class="status-indicator show realtime" id="statusIndicator">
        <span>实时翻译已启用</span>
    </div>

    <script>
        // 配置
        const API_BASE = 'http://localhost:8009';

        // 全局变量
        let currentSourceLang = 'zh';
        let currentTargetLang = 'en';
        let availableModels = [];
        let realtimeEnabled = true;
        let strictModeEnabled = false;
        let isTranslating = false;
        let realtimeTimeout = null;

        // DOM元素
        const elements = {
            inputText: document.getElementById('inputText'),
            outputText: document.getElementById('outputText'),
            translateBtn: document.getElementById('translateBtn'),
            loadingOverlay: document.getElementById('loadingOverlay'),
            inputCounter: document.getElementById('inputCounter'),
            outputCounter: document.getElementById('outputCounter'),
            toast: document.getElementById('toast'),
            swapBtn: document.getElementById('swapBtn'),
            sourceLang: document.getElementById('sourceLang'),
            targetLang: document.getElementById('targetLang'),
            clearInput: document.getElementById('clearInput'),
            clearAllBtn: document.getElementById('clearAllBtn'),
            copyOutput: document.getElementById('copyOutput'),
            pasteBtn: document.getElementById('pasteBtn'),
            downloadBtn: document.getElementById('downloadBtn'),
            modelSelect: document.getElementById('modelSelect'),
            refreshBtn: document.getElementById('refreshBtn'),
            realtimeToggle: document.getElementById('realtimeToggle'),
            strictModeToggle: document.getElementById('strictModeToggle'),
            statusIndicator: document.getElementById('statusIndicator'),
            streamingIndicator: document.getElementById('streamingIndicator'),
            shortcutFeedback: document.getElementById('shortcutFeedback'),
            translationStatus: document.getElementById('translationStatus')
        };

        // 语言配置
        const langConfig = {
            zh: { name: '中文', placeholder: '请输入要翻译的中文文本...' },
            en: { name: 'English', placeholder: 'Please enter English text to translate...' },
            ja: { name: '日本語', placeholder: '翻訳したい日本語のテキストを入力してください...' },
            ko: { name: '한국어', placeholder: '번역할 한국어 텍스트를 입력하세요...' },
            fr: { name: 'Français', placeholder: 'Veuillez saisir le texte français à traduire...' },
            de: { name: 'Deutsch', placeholder: 'Bitte geben Sie den zu übersetzenden deutschen Text ein...' },
            es: { name: 'Español', placeholder: 'Por favor, ingrese el texto en español para traducir...' },
            it: { name: 'Italiano', placeholder: 'Inserisci il testo italiano da tradurre...' },
            pt: { name: 'Português', placeholder: 'Digite o texto em português para traduzir...' },
            ru: { name: 'Русский', placeholder: 'Введите русский текст для перевода...' },
            ar: { name: 'العربية', placeholder: 'يرجى إدخال النص العربي للترجمة...' },
            hi: { name: 'हिन्दी', placeholder: 'कृपया अनुवाद के लिए हिंदी पाठ दर्ज करें...' },
            th: { name: 'ไทย', placeholder: 'กรุณาป้อนข้อความภาษาไทยเพื่อแปล...' },
            vi: { name: 'Tiếng Việt', placeholder: 'Vui lòng nhập văn bản tiếng Việt để dịch...' }
        };



        // 快捷键流式翻译函数
        let lastTranslatedText = '';
        let currentTranslationRequest = null;

        async function instantRealtimeTranslate() {
            if (!realtimeEnabled || isTranslating) return;

            const text = elements.inputText.value.trim();
            const selectedModel = elements.modelSelect.value;

            // 避免重复翻译相同内容
            if (text === lastTranslatedText) return;

            if (!text || !selectedModel) {
                elements.outputText.value = '';
                updateOutputCounter();
                lastTranslatedText = '';
                updateStatusIndicator(); // 恢复默认状态
                return;
            }

            // 取消之前的翻译请求
            if (currentTranslationRequest) {
                currentTranslationRequest.abort();
                currentTranslationRequest = null;
            }

            lastTranslatedText = text;
            // 快捷键翻译也使用流式输出，提升用户体验
            await performTranslation(text, selectedModel, true);
        }



        // 初始化
        async function init() {
            await loadModels();
            bindEvents();
            updateUI();
            updateCharCount();
            updateStatusIndicator();
        }

        // 加载模型列表
        async function loadModels() {
            try {
                showToast('正在加载模型列表...', 'info');
                const response = await fetch(`${API_BASE}/models`);
                const data = await response.json();

                availableModels = data.models || [];
                updateModelSelect();

                if (data.error) {
                    showToast(`模型加载失败: ${data.error}`, 'error');
                } else if (availableModels.length === 0) {
                    showToast('未获取到任何可用模型，请检查API配置', 'error');
                } else {
                    showToast(`成功加载 ${availableModels.length} 个模型`, 'success');
                }
            } catch (error) {
                console.error('Failed to load models:', error);
                showToast('网络错误，无法加载模型列表', 'error');
                availableModels = [];
                updateModelSelect();
            }
        }

        // 更新模型选择器
        function updateModelSelect() {
            elements.modelSelect.innerHTML = '';

            if (availableModels.length === 0) {
                elements.modelSelect.innerHTML = '<option value="">无可用模型 - 请刷新重试</option>';
                elements.modelSelect.disabled = true;
                return;
            }

            availableModels.forEach(model => {
                const option = document.createElement('option');
                option.value = model.id;
                option.textContent = model.id;
                elements.modelSelect.appendChild(option);
            });

            elements.modelSelect.disabled = false;
            if (availableModels.length > 0) {
                elements.modelSelect.value = availableModels[0].id;
            }
        }

        // 绑定事件
        function bindEvents() {
            // 模型相关
            elements.refreshBtn.addEventListener('click', loadModels);

            // 实时翻译开关
            elements.realtimeToggle.addEventListener('click', toggleRealtime);

            // 严格模式开关
            elements.strictModeToggle.addEventListener('click', toggleStrictMode);

            // 翻译相关
            elements.translateBtn.addEventListener('click', handleTranslate);

            elements.inputText.addEventListener('input', (e) => {
                updateCharCount();
            });

            // Ctrl+Enter 快捷键翻译
            elements.inputText.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' && e.ctrlKey) {
                    e.preventDefault();

                    // 防重复提交检查
                    if (isTranslating) {
                        // 已在翻译中，不做任何操作
                        return;
                    }

                    if (realtimeEnabled) {
                        instantRealtimeTranslate();
                    } else {
                        handleTranslate();
                    }
                }
            });

            // 语言切换
            elements.swapBtn.addEventListener('click', swapLanguages);
            elements.sourceLang.addEventListener('click', () => showLanguageSelector('source'));
            elements.targetLang.addEventListener('click', () => showLanguageSelector('target'));

            // 操作按钮
            elements.clearInput.addEventListener('click', () => {
                elements.inputText.value = '';
                elements.outputText.value = '';
                updateCharCount();
                updateOutputCounter();
            });

            elements.clearAllBtn.addEventListener('click', () => {
                elements.inputText.value = '';
                elements.outputText.value = '';
                updateCharCount();
                updateOutputCounter();
                hideToast();
            });

            elements.copyOutput.addEventListener('click', copyResult);
            elements.pasteBtn.addEventListener('click', pasteText);
            elements.downloadBtn.addEventListener('click', downloadResult);
        }

        // 切换实时翻译
        function toggleRealtime() {
            realtimeEnabled = !realtimeEnabled;
            elements.realtimeToggle.classList.toggle('active', realtimeEnabled);

            const toggleText = elements.realtimeToggle.querySelector('span:last-child');
            toggleText.textContent = realtimeEnabled ? '实时翻译' : '手动翻译';

            // 更新状态指示器
            updateStatusIndicator();

            showToast(realtimeEnabled ? '✅ 实时翻译已启用' : '⏸️ 实时翻译已关闭', 'info');

            // 关闭实时翻译时，取消正在进行的翻译
            if (!realtimeEnabled && currentTranslationRequest) {
                currentTranslationRequest.abort();
                currentTranslationRequest = null;
            }
        }

        // 切换严格模式
        function toggleStrictMode() {
            strictModeEnabled = !strictModeEnabled;
            elements.strictModeToggle.classList.toggle('active', strictModeEnabled);

            const toggleText = elements.strictModeToggle.querySelector('span:last-child');
            toggleText.textContent = strictModeEnabled ? '严格模式' : '普通模式';

            showToast(strictModeEnabled ? '🔒 严格翻译模式已启用' : '🔓 普通翻译模式已启用', 'info');

            // 严格模式切换时不自动翻译，等用户按回车
        }

        // 更新状态指示器
        function updateStatusIndicator(status = null, customMessage = '') {
            const indicator = elements.statusIndicator;
            const text = indicator.querySelector('span');

            if (status) {
                // 临时状态
                switch (status) {
                    case 'preparing':
                        indicator.className = 'status-indicator show preparing';
                        text.textContent = customMessage || '准备翻译...';
                        break;
                    case 'translating':
                        indicator.className = 'status-indicator show translating';
                        text.textContent = customMessage || '正在翻译...';
                        break;
                }
            } else {
                // 默认状态
                if (realtimeEnabled) {
                    indicator.className = 'status-indicator show realtime';
                    text.textContent = 'Ctrl+Enter 翻译';
                } else {
                    indicator.className = 'status-indicator show manual';
                    text.textContent = '手动翻译模式';
                }
            }
        }

        // 执行翻译的核心函数
        async function performTranslation(text, selectedModel, isRealtime = false) {
            if (isTranslating && isRealtime) return;

            isTranslating = true;

            // 显示翻译状态
            showTranslationStatus();
            hideToast();

            // 创建可取消的请求控制器
            const abortController = new AbortController();
            if (isRealtime) {
                currentTranslationRequest = abortController;
            }

            try {
                // 优先使用流式输出，提升用户体验
                const useStream = 'ReadableStream' in window;

                const response = await fetch(`${API_BASE}/translate${useStream ? '_stream' : ''}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        text: text,
                        source_lang: currentSourceLang,
                        target_lang: currentTargetLang,
                        model: selectedModel,
                        stream: useStream,
                        strict_mode: strictModeEnabled
                    }),
                    signal: abortController.signal  // 添加取消信号
                });

                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.detail || '翻译失败');
                }

                if (useStream && response.body) {
                    // 流式处理
                    await handleStreamResponse(response, isRealtime);
                } else {
                    // 普通处理
                    const data = await response.json();
                    elements.outputText.value = data.translated_text;
                    updateOutputCounter();
                    // 静默完成，不显示任何提示
                }
            } catch (error) {
                // 如果是取消请求，不显示错误
                if (error.name === 'AbortError') {
                    console.log('Translation request was cancelled');
                    return;
                }

                console.error('Translation error:', error);
                // 静默处理错误，不显示Toast
            } finally {
                isTranslating = false;
                if (isRealtime) {
                    currentTranslationRequest = null;
                }
                // 隐藏翻译状态
                hideTranslationStatus();
                setLoading(false);
            }
        }

        // 处理流式响应
        async function handleStreamResponse(response, isRealtime) {
            const reader = response.body.getReader();
            const decoder = new TextDecoder();
            let translatedText = '';
            let updateBuffer = '';
            let lastUpdateTime = 0;
            const UPDATE_INTERVAL = 500; // 500ms更新一次，确保无频闪

            // 清空输出框，直接开始翻译
            elements.outputText.value = '';

            // 使用强化防抖机制，不需要额外定时器

            // 强化防抖更新函数
            let updateTimeout = null;
            const smoothUpdate = () => {
                // 清除之前的定时器
                if (updateTimeout) {
                    clearTimeout(updateTimeout);
                }

                // 设置新的延迟更新
                updateTimeout = setTimeout(() => {
                    if (updateBuffer) {
                        // 批量更新DOM，减少重绘
                        translatedText += updateBuffer;

                        // 检测滚动位置
                        const wasAtBottom = elements.outputText.scrollTop >=
                            elements.outputText.scrollHeight - elements.outputText.clientHeight - 10;

                        // 使用requestAnimationFrame确保流畅更新
                        requestAnimationFrame(() => {
                            elements.outputText.value = translatedText;
                            updateOutputCounter();

                            // 只有用户在底部时才自动滚动
                            if (wasAtBottom) {
                                elements.outputText.scrollTop = elements.outputText.scrollHeight;
                            }
                        });

                        updateBuffer = '';
                        lastUpdateTime = Date.now();
                    }
                }, UPDATE_INTERVAL);
            };

            try {
                while (true) {
                    const { done, value } = await reader.read();

                    if (done) break;

                    const chunk = decoder.decode(value, { stream: true });
                    const lines = chunk.split('\n');

                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            try {
                                const data = JSON.parse(line.slice(6));

                                if (data.type === 'chunk_info') {
                                    // 静默处理分块信息，不显示进度
                                    // 用户只看到翻译结果，不看到加载状态
                                } else if (data.type === 'chunk') {
                                    // 将内容添加到缓冲区
                                    updateBuffer += data.content;

                                    // 强化防抖更新，彻底解决频闪
                                    smoothUpdate();
                                } else if (data.type === 'done') {
                                    // 最终更新，确保所有内容都显示
                                    if (updateBuffer) {
                                        translatedText += updateBuffer;
                                        elements.outputText.value = translatedText;
                                        updateOutputCounter();
                                        requestAnimationFrame(() => {
                                            elements.outputText.scrollTop = elements.outputText.scrollHeight;
                                        });
                                    }
                                    break;
                                } else if (data.type === 'error') {
                                    throw new Error(data.message || '翻译过程中出现错误');
                                }
                            } catch (parseError) {
                                // 忽略JSON解析错误，可能是不完整的数据块
                                continue;
                            }
                        }
                    }
                }
            } finally {
                reader.releaseLock();

                // 清理防抖定时器
                if (updateTimeout) {
                    clearTimeout(updateTimeout);
                }

                // 最终确保所有缓冲内容都显示
                if (updateBuffer) {
                    translatedText += updateBuffer;
                    requestAnimationFrame(() => {
                        elements.outputText.value = translatedText;
                        updateOutputCounter();
                        elements.outputText.scrollTop = elements.outputText.scrollHeight;
                    });
                }

                // 静默完成，不显示任何状态
            }
        }

        // 手动翻译
        async function handleTranslate() {
            // 防重复提交检查
            if (isTranslating) {
                return; // 静默忽略，因为已有视觉提示
            }

            const text = elements.inputText.value.trim();
            const selectedModel = elements.modelSelect.value;

            if (!text) {
                showToast('请输入要翻译的文本', 'error');
                return;
            }

            if (!selectedModel) {
                showToast('请选择翻译模型', 'error');
                return;
            }

            await performTranslation(text, selectedModel, false);
        }

        // 交换语言
        function swapLanguages() {
            [currentSourceLang, currentTargetLang] = [currentTargetLang, currentSourceLang];

            // 交换文本
            const temp = elements.inputText.value;
            elements.inputText.value = elements.outputText.value;
            elements.outputText.value = temp;

            updateUI();
            updateCharCount();
            updateOutputCounter();
            hideToast();

            // 语言交换后不自动翻译，等用户按回车
        }

        // 显示语言选择器
        function showLanguageSelector(type) {
            const languages = Object.keys(langConfig);
            const currentLang = type === 'source' ? currentSourceLang : currentTargetLang;

            const selector = document.createElement('div');
            selector.className = 'language-selector';
            selector.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: white;
                border: 1px solid var(--border-color);
                border-radius: var(--border-radius-lg);
                box-shadow: var(--shadow-lg);
                z-index: 1000;
                max-height: 400px;
                overflow-y: auto;
                min-width: 200px;
            `;

            const overlay = document.createElement('div');
            overlay.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.5);
                z-index: 999;
            `;

            languages.forEach(lang => {
                const option = document.createElement('div');
                option.textContent = langConfig[lang].name;
                option.style.cssText = `
                    padding: 0.75rem 1rem;
                    cursor: pointer;
                    border-bottom: 1px solid var(--border-color);
                    transition: var(--transition);
                    ${lang === currentLang ? 'background: var(--primary-color); color: white;' : ''}
                `;

                option.addEventListener('mouseenter', () => {
                    if (lang !== currentLang) {
                        option.style.background = 'var(--bg-tertiary)';
                    }
                });

                option.addEventListener('mouseleave', () => {
                    if (lang !== currentLang) {
                        option.style.background = '';
                    }
                });

                option.addEventListener('click', () => {
                    if (type === 'source') {
                        currentSourceLang = lang;
                    } else {
                        currentTargetLang = lang;
                    }
                    updateUI();
                    document.body.removeChild(overlay);
                    document.body.removeChild(selector);
                });

                selector.appendChild(option);
            });

            overlay.addEventListener('click', () => {
                document.body.removeChild(overlay);
                document.body.removeChild(selector);
            });

            document.body.appendChild(overlay);
            document.body.appendChild(selector);
        }

        // 更新UI
        function updateUI() {
            const sourceConfig = langConfig[currentSourceLang];
            const targetConfig = langConfig[currentTargetLang];

            elements.sourceLang.textContent = sourceConfig.name;
            elements.targetLang.textContent = targetConfig.name;

            elements.inputText.placeholder = sourceConfig.placeholder;
            elements.outputText.placeholder = `${targetConfig.name}翻译结果将显示在这里...`;
        }

        // 更新字符计数
        function updateCharCount() {
            const count = elements.inputText.value.length;
            elements.inputCounter.textContent = `${count} / 1000000`;

            if (count > 900000) {
                elements.inputCounter.style.color = '#dc2626';
            } else if (count > 600000) {
                elements.inputCounter.style.color = '#f59e0b';
            } else {
                elements.inputCounter.style.color = '#64748b';
            }
        }

        // 更新输出计数
        function updateOutputCounter() {
            const count = elements.outputText.value.length;
            elements.outputCounter.textContent = `${count} 字符`;
        }

        // 复制结果
        async function copyResult() {
            const text = elements.outputText.value.trim();

            if (!text) {
                showToast('没有可复制的内容', 'error');
                return;
            }

            try {
                await navigator.clipboard.writeText(text);
                showToast('复制成功！', 'success');
            } catch (error) {
                // 降级方案
                elements.outputText.select();
                try {
                    document.execCommand('copy');
                    showToast('复制成功！', 'success');
                } catch (e) {
                    showToast('复制失败，请手动选择复制', 'error');
                }
            }
        }

        // 粘贴文本
        async function pasteText() {
            try {
                const text = await navigator.clipboard.readText();
                if (text) {
                    elements.inputText.value = text.substring(0, 1000000);
                    updateCharCount();
                    showToast('粘贴成功！', 'success');

                    // 粘贴后不自动翻译，等用户按回车
                } else {
                    showToast('剪贴板为空', 'error');
                }
            } catch (error) {
                showToast('无法访问剪贴板，请手动粘贴', 'error');
            }
        }

        // 下载结果
        function downloadResult() {
            const text = elements.outputText.value.trim();

            if (!text) {
                showToast('没有可下载的内容', 'error');
                return;
            }

            const blob = new Blob([text], { type: 'text/plain;charset=utf-8' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `translation_${new Date().toISOString().slice(0, 10)}.txt`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            showToast('下载成功！', 'success');
        }

        // 设置加载状态
        function setLoading(loading) {
            if (loading) {
                elements.loadingOverlay.classList.add('active');
                elements.translateBtn.disabled = true;
                elements.translateBtn.textContent = '翻译中...';
                elements.translateBtn.style.opacity = '0.6';
            } else {
                elements.loadingOverlay.classList.remove('active');
                elements.translateBtn.disabled = false;
                elements.translateBtn.textContent = '立即翻译';
                elements.translateBtn.style.opacity = '1';
            }
        }

        // 显示Toast
        function showToast(message, type = 'info') {
            elements.toast.textContent = message;
            elements.toast.className = `toast show ${type}`;

            setTimeout(() => {
                hideToast();
            }, 3000);
        }

        // 隐藏Toast
        function hideToast() {
            elements.toast.classList.remove('show');
        }

        // 显示翻译状态
        function showTranslationStatus() {
            // 显示彩带
            elements.shortcutFeedback.classList.add('active');
            // 显示状态提示
            elements.translationStatus.classList.add('show');
        }

        // 隐藏翻译状态
        function hideTranslationStatus() {
            // 隐藏彩带
            elements.shortcutFeedback.classList.remove('active');
            // 隐藏状态提示
            elements.translationStatus.classList.remove('show');
        }

        // 启动应用
        document.addEventListener('DOMContentLoaded', init);
    </script>
</body>
</html>
