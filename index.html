<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI智能翻译</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🔄</text></svg>">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-color: #4f46e5;
            --primary-hover: #4338ca;
            --secondary-color: #6b7280;
            --success-color: #10b981;
            --error-color: #ef4444;
            
            --bg-primary: #ffffff;
            --bg-secondary: #f8fafc;
            --bg-tertiary: #f1f5f9;
            
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --text-muted: #94a3b8;
            
            --border-color: #e2e8f0;
            --border-hover: #cbd5e1;
            --border-focus: #4f46e5;
            
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            
            --border-radius: 8px;
            --border-radius-lg: 12px;
            --border-radius-xl: 16px;
            
            --transition: all 0.2s ease-in-out;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: var(--bg-secondary);
            color: var(--text-primary);
            line-height: 1.6;
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 1rem;
        }

        .header {
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-md);
            padding: 1.5rem 2rem;
            margin-bottom: 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .logo-section {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .logo {
            font-size: 1.8rem;
            font-weight: 700;
            color: var(--primary-color);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .logo::before {
            content: '🌐';
            font-size: 1.5rem;
        }

        .subtitle {
            font-size: 0.9rem;
            color: var(--text-secondary);
            margin-left: 0.5rem;
        }

        .model-control {
            display: flex;
            align-items: center;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .model-label {
            font-size: 0.9rem;
            color: var(--text-secondary);
            font-weight: 600;
        }

        .model-select {
            padding: 0.75rem 1rem;
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius);
            background: var(--bg-primary);
            font-size: 0.9rem;
            color: var(--text-primary);
            min-width: 200px;
            transition: var(--transition);
            cursor: pointer;
        }

        .model-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
        }

        .refresh-btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: var(--border-radius);
            background: var(--primary-color);
            color: white;
            font-weight: 600;
            font-size: 0.9rem;
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .refresh-btn:hover {
            background: var(--primary-hover);
            transform: translateY(-1px);
        }

        .realtime-toggle {
            background: transparent;
            color: var(--text-secondary);
            border: 2px solid var(--border-color);
            border-radius: 50px;
            padding: 0.5rem 1rem;
            font-size: 0.85rem;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .realtime-toggle.active {
            background: var(--success-color);
            border-color: var(--success-color);
            color: white;
        }

        .realtime-toggle:hover {
            transform: translateY(-1px);
        }

        .main-card {
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-lg);
            overflow: hidden;
        }

        .language-control {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 1.5rem;
            border-bottom: 1px solid var(--border-color);
            gap: 2rem;
            background: var(--bg-tertiary);
        }

        .language-btn {
            padding: 0.8rem 2rem;
            border: 2px solid var(--border-color);
            border-radius: 50px;
            background: var(--bg-primary);
            color: var(--text-primary);
            font-weight: 600;
            font-size: 1rem;
            cursor: pointer;
            transition: var(--transition);
            min-width: 120px;
        }

        .language-btn:hover {
            border-color: var(--primary-color);
            background: var(--primary-color);
            color: white;
            transform: translateY(-2px);
        }

        .swap-btn {
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            font-size: 1.2rem;
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .swap-btn:hover {
            background: var(--primary-hover);
            transform: rotate(180deg) scale(1.1);
        }

        .translation-panels {
            display: grid;
            grid-template-columns: 1fr 1fr;
            min-height: 600px;
        }

        .text-panel {
            position: relative;
            display: flex;
            flex-direction: column;
        }

        .text-panel:first-child {
            border-right: 1px solid var(--border-color);
        }

        .panel-header {
            padding: 1rem 1.5rem;
            background: var(--bg-tertiary);
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .panel-title {
            font-weight: 600;
            color: var(--text-primary);
            font-size: 0.9rem;
        }

        .char-counter {
            font-size: 0.8rem;
            color: var(--text-muted);
        }

        .text-area {
            flex: 1;
            padding: 2rem;
            border: none;
            resize: none;
            font-size: 1.1rem;
            line-height: 1.7;
            background: transparent;
            color: var(--text-primary);
            outline: none;
            font-family: inherit;
            min-height: 500px;
        }

        .text-area::placeholder {
            color: var(--text-muted);
            font-style: italic;
        }

        .text-area:focus {
            background: rgba(79, 70, 229, 0.02);
        }

        .output-panel {
            background: var(--bg-secondary);
        }

        .output-panel .text-area {
            background: transparent;
            color: var(--text-secondary);
        }

        .panel-actions {
            position: absolute;
            bottom: 1rem;
            right: 1rem;
            display: flex;
            gap: 0.5rem;
        }

        .action-btn {
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: 0.5rem;
            cursor: pointer;
            transition: var(--transition);
            font-size: 1rem;
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: var(--shadow-sm);
        }

        .action-btn:hover {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
            transform: translateY(-1px);
        }

        .control-panel {
            padding: 1.5rem;
            text-align: center;
            border-top: 1px solid var(--border-color);
            background: var(--bg-tertiary);
        }

        .translate-btn {
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 50px;
            padding: 1rem 3rem;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            box-shadow: var(--shadow-md);
            margin-right: 1rem;
        }

        .translate-btn:hover {
            background: var(--primary-hover);
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .translate-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .secondary-btn {
            background: transparent;
            color: var(--text-secondary);
            border: 2px solid var(--border-color);
            border-radius: 50px;
            padding: 1rem 2rem;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
        }

        .secondary-btn:hover {
            border-color: var(--primary-color);
            color: var(--primary-color);
            background: rgba(79, 70, 229, 0.05);
        }

        /* 状态指示器 */
        .status-indicator {
            position: fixed;
            top: 1rem;
            right: 1rem;
            padding: 0.5rem 1rem;
            border-radius: 50px;
            font-size: 0.8rem;
            font-weight: 600;
            z-index: 1000;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            background: var(--success-color);
            color: white;
            box-shadow: var(--shadow-md);
            opacity: 0;
            transform: translateX(100%);
            transition: var(--transition);
        }

        .status-indicator.show {
            opacity: 1;
            transform: translateX(0);
        }

        .status-indicator.realtime::before {
            content: '⚡';
            animation: pulse 2s infinite;
        }

        .status-indicator.manual {
            background: var(--secondary-color);
        }

        .status-indicator.manual::before {
            content: '⏸️';
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .header {
                flex-direction: column;
                text-align: center;
            }

            .model-control {
                justify-content: center;
                width: 100%;
            }

            .model-select {
                min-width: auto;
                width: 100%;
                max-width: 300px;
            }

            .translation-panels {
                grid-template-columns: 1fr;
            }

            .text-panel:first-child {
                border-right: none;
                border-bottom: 1px solid var(--border-color);
            }

            .language-control {
                flex-direction: column;
                gap: 1rem;
            }

            .translate-btn, .secondary-btn {
                width: 100%;
                margin: 0.5rem 0;
            }

            .text-area {
                padding: 1.5rem;
                font-size: 1rem;
                min-height: 300px;
            }
        }

        /* 加载状态 */
        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            visibility: hidden;
            transition: var(--transition);
        }

        .loading-overlay.active {
            opacity: 1;
            visibility: visible;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid var(--border-color);
            border-top: 3px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Toast 通知 */
        .toast {
            position: fixed;
            top: 2rem;
            right: 2rem;
            padding: 1rem 1.5rem;
            border-radius: var(--border-radius);
            color: white;
            font-weight: 600;
            z-index: 2000;
            transform: translateX(100%);
            transition: var(--transition);
            box-shadow: var(--shadow-lg);
        }

        .toast.show {
            transform: translateX(0);
        }

        .toast.success {
            background: var(--success-color);
        }

        .toast.error {
            background: var(--error-color);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo-section">
                <div class="logo">AI智能翻译</div>
                <div class="subtitle">基于先进的人工智能技术，提供专业多语言翻译服务</div>
            </div>
            <div class="model-control">
                <span class="model-label">🤖 翻译模型:</span>
                <select class="model-select" id="modelSelect">
                    <option value="">正在加载模型...</option>
                </select>
                <button class="refresh-btn" id="refreshBtn">
                    <span>🔄</span>
                    刷新模型
                </button>
                <button class="realtime-toggle active" id="realtimeToggle">
                    <span>⚡</span>
                    <span>实时翻译</span>
                </button>
            </div>
        </div>

        <div class="main-card">
            <div class="language-control">
                <button class="language-btn" id="sourceLang">中文</button>
                <button class="swap-btn" id="swapBtn" title="交换语言">⇄</button>
                <button class="language-btn" id="targetLang">English</button>
            </div>

            <div class="translation-panels">
                <div class="text-panel">
                    <div class="panel-header">
                        <div class="panel-title">输入文本</div>
                        <div class="char-counter" id="inputCounter">0 / 5000</div>
                    </div>
                    <textarea 
                        class="text-area" 
                        id="inputText" 
                        placeholder="请输入要翻译的文本..."
                        maxlength="5000"
                    ></textarea>
                    <div class="panel-actions">
                        <button class="action-btn" id="clearInput" title="清空">🗑️</button>
                        <button class="action-btn" id="pasteBtn" title="粘贴">📋</button>
                    </div>
                </div>

                <div class="text-panel output-panel">
                    <div class="panel-header">
                        <div class="panel-title">翻译结果</div>
                        <div class="char-counter" id="outputCounter">0 字符</div>
                    </div>
                    <textarea 
                        class="text-area" 
                        id="outputText" 
                        placeholder="翻译结果将在这里显示..."
                        readonly
                    ></textarea>
                    <div class="panel-actions">
                        <button class="action-btn" id="copyOutput" title="复制">📄</button>
                        <button class="action-btn" id="downloadBtn" title="下载">💾</button>
                    </div>
                </div>
            </div>

            <div class="control-panel">
                <button class="translate-btn" id="translateBtn">立即翻译</button>
                <button class="secondary-btn" id="clearAllBtn">清空全部</button>
            </div>

            <div class="loading-overlay" id="loadingOverlay">
                <div class="loading-spinner"></div>
            </div>
        </div>
    </div>

    <div class="toast" id="toast"></div>
    <div class="status-indicator show realtime" id="statusIndicator">
        <span>实时翻译已启用</span>
    </div>

    <script>
        // 配置
        const API_BASE = 'http://localhost:8009';

        // 全局变量
        let currentSourceLang = 'zh';
        let currentTargetLang = 'en';
        let availableModels = [];
        let realtimeEnabled = true;
        let isTranslating = false;
        let realtimeTimeout = null;

        // DOM元素
        const elements = {
            inputText: document.getElementById('inputText'),
            outputText: document.getElementById('outputText'),
            translateBtn: document.getElementById('translateBtn'),
            loadingOverlay: document.getElementById('loadingOverlay'),
            inputCounter: document.getElementById('inputCounter'),
            outputCounter: document.getElementById('outputCounter'),
            toast: document.getElementById('toast'),
            swapBtn: document.getElementById('swapBtn'),
            sourceLang: document.getElementById('sourceLang'),
            targetLang: document.getElementById('targetLang'),
            clearInput: document.getElementById('clearInput'),
            clearAllBtn: document.getElementById('clearAllBtn'),
            copyOutput: document.getElementById('copyOutput'),
            pasteBtn: document.getElementById('pasteBtn'),
            downloadBtn: document.getElementById('downloadBtn'),
            modelSelect: document.getElementById('modelSelect'),
            refreshBtn: document.getElementById('refreshBtn'),
            realtimeToggle: document.getElementById('realtimeToggle'),
            statusIndicator: document.getElementById('statusIndicator')
        };

        // 语言配置
        const langConfig = {
            zh: { name: '中文', placeholder: '请输入要翻译的中文文本...' },
            en: { name: 'English', placeholder: 'Please enter English text to translate...' },
            ja: { name: '日本語', placeholder: '翻訳したい日本語のテキストを入力してください...' },
            ko: { name: '한국어', placeholder: '번역할 한국어 텍스트를 입력하세요...' },
            fr: { name: 'Français', placeholder: 'Veuillez saisir le texte français à traduire...' },
            de: { name: 'Deutsch', placeholder: 'Bitte geben Sie den zu übersetzenden deutschen Text ein...' },
            es: { name: 'Español', placeholder: 'Por favor, ingrese el texto en español para traducir...' },
            it: { name: 'Italiano', placeholder: 'Inserisci il testo italiano da tradurre...' },
            pt: { name: 'Português', placeholder: 'Digite o texto em português para traduzir...' },
            ru: { name: 'Русский', placeholder: 'Введите русский текст для перевода...' },
            ar: { name: 'العربية', placeholder: 'يرجى إدخال النص العربي للترجمة...' },
            hi: { name: 'हिन्दी', placeholder: 'कृपया अनुवाद के लिए हिंदी पाठ दर्ज करें...' },
            th: { name: 'ไทย', placeholder: 'กรุณาป้อนข้อความภาษาไทยเพื่อแปล...' },
            vi: { name: 'Tiếng Việt', placeholder: 'Vui lòng nhập văn bản tiếng Việt để dịch...' }
        };

        // 防抖函数
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        // 实时翻译函数
        const realtimeTranslate = debounce(async () => {
            if (!realtimeEnabled || isTranslating) return;

            const text = elements.inputText.value.trim();
            const selectedModel = elements.modelSelect.value;

            if (!text || !selectedModel) {
                elements.outputText.value = '';
                updateOutputCounter();
                return;
            }

            await performTranslation(text, selectedModel, true);
        }, 1200);

        // 初始化
        async function init() {
            await loadModels();
            bindEvents();
            updateUI();
            updateCharCount();
            updateStatusIndicator();
        }

        // 加载模型列表
        async function loadModels() {
            try {
                showToast('正在加载模型列表...', 'info');
                const response = await fetch(`${API_BASE}/models`);
                const data = await response.json();

                availableModels = data.models || [];
                updateModelSelect();

                if (data.error) {
                    showToast(`模型加载失败: ${data.error}`, 'error');
                } else if (availableModels.length === 0) {
                    showToast('未获取到任何可用模型，请检查API配置', 'error');
                } else {
                    showToast(`成功加载 ${availableModels.length} 个模型`, 'success');
                }
            } catch (error) {
                console.error('Failed to load models:', error);
                showToast('网络错误，无法加载模型列表', 'error');
                availableModels = [];
                updateModelSelect();
            }
        }

        // 更新模型选择器
        function updateModelSelect() {
            elements.modelSelect.innerHTML = '';

            if (availableModels.length === 0) {
                elements.modelSelect.innerHTML = '<option value="">无可用模型 - 请刷新重试</option>';
                elements.modelSelect.disabled = true;
                return;
            }

            availableModels.forEach(model => {
                const option = document.createElement('option');
                option.value = model.id;
                option.textContent = model.id;
                elements.modelSelect.appendChild(option);
            });

            elements.modelSelect.disabled = false;
            if (availableModels.length > 0) {
                elements.modelSelect.value = availableModels[0].id;
            }
        }

        // 绑定事件
        function bindEvents() {
            // 模型相关
            elements.refreshBtn.addEventListener('click', loadModels);

            // 实时翻译开关
            elements.realtimeToggle.addEventListener('click', toggleRealtime);

            // 翻译相关
            elements.translateBtn.addEventListener('click', handleTranslate);
            elements.inputText.addEventListener('input', (e) => {
                updateCharCount();
                if (realtimeEnabled) {
                    realtimeTranslate();
                }
            });
            elements.inputText.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' && e.ctrlKey) {
                    e.preventDefault();
                    handleTranslate();
                }
            });

            // 语言切换
            elements.swapBtn.addEventListener('click', swapLanguages);
            elements.sourceLang.addEventListener('click', () => showLanguageSelector('source'));
            elements.targetLang.addEventListener('click', () => showLanguageSelector('target'));

            // 操作按钮
            elements.clearInput.addEventListener('click', () => {
                elements.inputText.value = '';
                elements.outputText.value = '';
                updateCharCount();
                updateOutputCounter();
            });

            elements.clearAllBtn.addEventListener('click', () => {
                elements.inputText.value = '';
                elements.outputText.value = '';
                updateCharCount();
                updateOutputCounter();
                hideToast();
            });

            elements.copyOutput.addEventListener('click', copyResult);
            elements.pasteBtn.addEventListener('click', pasteText);
            elements.downloadBtn.addEventListener('click', downloadResult);
        }

        // 切换实时翻译
        function toggleRealtime() {
            realtimeEnabled = !realtimeEnabled;
            elements.realtimeToggle.classList.toggle('active', realtimeEnabled);

            const toggleText = elements.realtimeToggle.querySelector('span:last-child');
            toggleText.textContent = realtimeEnabled ? '实时翻译' : '手动翻译';

            // 更新状态指示器
            updateStatusIndicator();

            showToast(realtimeEnabled ? '✅ 实时翻译已启用' : '⏸️ 实时翻译已关闭', 'info');

            // 如果启用实时翻译且有文本，立即翻译
            if (realtimeEnabled && elements.inputText.value.trim()) {
                realtimeTranslate();
            }
        }

        // 更新状态指示器
        function updateStatusIndicator() {
            const indicator = elements.statusIndicator;
            const text = indicator.querySelector('span');

            if (realtimeEnabled) {
                indicator.className = 'status-indicator show realtime';
                text.textContent = '实时翻译已启用';
            } else {
                indicator.className = 'status-indicator show manual';
                text.textContent = '手动翻译模式';
            }
        }

        // 执行翻译的核心函数
        async function performTranslation(text, selectedModel, isRealtime = false) {
            if (isTranslating && isRealtime) return;

            isTranslating = true;

            if (!isRealtime) {
                setLoading(true);
                hideToast();
            }

            try {
                const response = await fetch(`${API_BASE}/translate`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        text: text,
                        source_lang: currentSourceLang,
                        target_lang: currentTargetLang,
                        model: selectedModel
                    })
                });

                const data = await response.json();

                if (response.ok) {
                    elements.outputText.value = data.translated_text;
                    updateOutputCounter();
                    if (!isRealtime) {
                        showToast('✅ 翻译完成！', 'success');
                    }
                } else {
                    throw new Error(data.detail || '翻译失败');
                }
            } catch (error) {
                console.error('Translation error:', error);
                if (!isRealtime) {
                    showToast(error.message || '翻译服务暂时不可用，请稍后重试', 'error');
                }
            } finally {
                isTranslating = false;
                if (!isRealtime) {
                    setLoading(false);
                }
            }
        }

        // 手动翻译
        async function handleTranslate() {
            const text = elements.inputText.value.trim();
            const selectedModel = elements.modelSelect.value;

            if (!text) {
                showToast('请输入要翻译的文本', 'error');
                return;
            }

            if (!selectedModel) {
                showToast('请选择翻译模型', 'error');
                return;
            }

            await performTranslation(text, selectedModel, false);
        }

        // 交换语言
        function swapLanguages() {
            [currentSourceLang, currentTargetLang] = [currentTargetLang, currentSourceLang];

            // 交换文本
            const temp = elements.inputText.value;
            elements.inputText.value = elements.outputText.value;
            elements.outputText.value = temp;

            updateUI();
            updateCharCount();
            updateOutputCounter();
            hideToast();

            // 如果开启实时翻译且有文本，立即翻译
            if (realtimeEnabled && elements.inputText.value.trim()) {
                realtimeTranslate();
            }
        }

        // 显示语言选择器
        function showLanguageSelector(type) {
            const languages = Object.keys(langConfig);
            const currentLang = type === 'source' ? currentSourceLang : currentTargetLang;

            const selector = document.createElement('div');
            selector.className = 'language-selector';
            selector.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: white;
                border: 1px solid var(--border-color);
                border-radius: var(--border-radius-lg);
                box-shadow: var(--shadow-lg);
                z-index: 1000;
                max-height: 400px;
                overflow-y: auto;
                min-width: 200px;
            `;

            const overlay = document.createElement('div');
            overlay.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.5);
                z-index: 999;
            `;

            languages.forEach(lang => {
                const option = document.createElement('div');
                option.textContent = langConfig[lang].name;
                option.style.cssText = `
                    padding: 0.75rem 1rem;
                    cursor: pointer;
                    border-bottom: 1px solid var(--border-color);
                    transition: var(--transition);
                    ${lang === currentLang ? 'background: var(--primary-color); color: white;' : ''}
                `;

                option.addEventListener('mouseenter', () => {
                    if (lang !== currentLang) {
                        option.style.background = 'var(--bg-tertiary)';
                    }
                });

                option.addEventListener('mouseleave', () => {
                    if (lang !== currentLang) {
                        option.style.background = '';
                    }
                });

                option.addEventListener('click', () => {
                    if (type === 'source') {
                        currentSourceLang = lang;
                    } else {
                        currentTargetLang = lang;
                    }
                    updateUI();
                    document.body.removeChild(overlay);
                    document.body.removeChild(selector);
                });

                selector.appendChild(option);
            });

            overlay.addEventListener('click', () => {
                document.body.removeChild(overlay);
                document.body.removeChild(selector);
            });

            document.body.appendChild(overlay);
            document.body.appendChild(selector);
        }

        // 更新UI
        function updateUI() {
            const sourceConfig = langConfig[currentSourceLang];
            const targetConfig = langConfig[currentTargetLang];

            elements.sourceLang.textContent = sourceConfig.name;
            elements.targetLang.textContent = targetConfig.name;

            elements.inputText.placeholder = sourceConfig.placeholder;
            elements.outputText.placeholder = `${targetConfig.name}翻译结果将显示在这里...`;
        }

        // 更新字符计数
        function updateCharCount() {
            const count = elements.inputText.value.length;
            elements.inputCounter.textContent = `${count} / 5000`;

            if (count > 4500) {
                elements.inputCounter.style.color = '#dc2626';
            } else if (count > 4000) {
                elements.inputCounter.style.color = '#d97706';
            } else {
                elements.inputCounter.style.color = '#64748b';
            }
        }

        // 更新输出计数
        function updateOutputCounter() {
            const count = elements.outputText.value.length;
            elements.outputCounter.textContent = `${count} 字符`;
        }

        // 复制结果
        async function copyResult() {
            const text = elements.outputText.value.trim();

            if (!text) {
                showToast('没有可复制的内容', 'error');
                return;
            }

            try {
                await navigator.clipboard.writeText(text);
                showToast('复制成功！', 'success');
            } catch (error) {
                // 降级方案
                elements.outputText.select();
                try {
                    document.execCommand('copy');
                    showToast('复制成功！', 'success');
                } catch (e) {
                    showToast('复制失败，请手动选择复制', 'error');
                }
            }
        }

        // 粘贴文本
        async function pasteText() {
            try {
                const text = await navigator.clipboard.readText();
                if (text) {
                    elements.inputText.value = text.substring(0, 5000);
                    updateCharCount();
                    showToast('粘贴成功！', 'success');

                    // 如果开启实时翻译，立即翻译
                    if (realtimeEnabled) {
                        realtimeTranslate();
                    }
                } else {
                    showToast('剪贴板为空', 'error');
                }
            } catch (error) {
                showToast('无法访问剪贴板，请手动粘贴', 'error');
            }
        }

        // 下载结果
        function downloadResult() {
            const text = elements.outputText.value.trim();

            if (!text) {
                showToast('没有可下载的内容', 'error');
                return;
            }

            const blob = new Blob([text], { type: 'text/plain;charset=utf-8' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `translation_${new Date().toISOString().slice(0, 10)}.txt`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            showToast('下载成功！', 'success');
        }

        // 设置加载状态
        function setLoading(loading) {
            if (loading) {
                elements.loadingOverlay.classList.add('active');
                elements.translateBtn.disabled = true;
                elements.translateBtn.textContent = '翻译中...';
            } else {
                elements.loadingOverlay.classList.remove('active');
                elements.translateBtn.disabled = false;
                elements.translateBtn.textContent = '立即翻译';
            }
        }

        // 显示Toast
        function showToast(message, type = 'info') {
            elements.toast.textContent = message;
            elements.toast.className = `toast show ${type}`;

            setTimeout(() => {
                hideToast();
            }, 3000);
        }

        // 隐藏Toast
        function hideToast() {
            elements.toast.classList.remove('show');
        }

        // 启动应用
        document.addEventListener('DOMContentLoaded', init);
    </script>
</body>
</html>
