
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🌐 AI智能翻译工具 - 专业版</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🌐</text></svg>">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2563eb;
            --primary-hover: #1d4ed8;
            --primary-light: #3b82f6;
            --secondary-color: #64748b;
            --success-color: #059669;
            --success-light: #10b981;
            --warning-color: #d97706;
            --error-color: #dc2626;
            --bg-color: #f8fafc;
            --bg-secondary: #f1f5f9;
            --bg-tertiary: #e2e8f0;
            --text-primary: #1e293b;
            --text-secondary: #475569;
            --text-muted: #64748b;
            --border-color: #e2e8f0;
            --border-hover: #cbd5e1;
            --white: #ffffff;
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
            --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            --border-radius: 12px;
            --border-radius-lg: 16px;
            --border-radius-xl: 20px;
            --transition: all 0.2s ease-in-out;
            --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: var(--text-primary);
            line-height: 1.6;
            min-height: 100vh;
            padding: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .container {
            background: var(--white);
            border-radius: var(--border-radius-xl);
            box-shadow: var(--shadow-2xl);
            width: 100%;
            max-width: 1000px;
            overflow: hidden;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .header {
            background: var(--gradient-primary);
            padding: 2rem;
            text-align: center;
            color: var(--white);
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            pointer-events: none;
        }

        .header-content {
            position: relative;
            z-index: 1;
        }

        .logo {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .logo-icon {
            font-size: 3rem;
            animation: float 3s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: 0.5rem;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
            font-weight: 400;
        }

        .main-content {
            padding: 2.5rem;
        }

        .config-section {
            background: var(--bg-secondary);
            border-radius: var(--border-radius-lg);
            padding: 2rem;
            margin-bottom: 2rem;
            border: 1px solid var(--border-color);
        }

        .config-title {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            font-size: 0.9rem;
            font-weight: 600;
            color: var(--text-secondary);
            margin-bottom: 0.5rem;
        }

        .form-input, .form-select {
            width: 100%;
            padding: 0.875rem 1rem;
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius);
            font-size: 1rem;
            background: var(--white);
            color: var(--text-primary);
            transition: var(--transition);
            font-family: inherit;
        }

        .form-input:focus, .form-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        .form-input[type="password"] {
            letter-spacing: 2px;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            padding: 0.875rem 1.5rem;
            border: none;
            border-radius: var(--border-radius);
            font-size: 0.9rem;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            text-decoration: none;
            font-family: inherit;
        }

        .btn-primary {
            background: var(--primary-color);
            color: var(--white);
            box-shadow: var(--shadow-md);
        }

        .btn-primary:hover:not(:disabled) {
            background: var(--primary-hover);
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .btn-success {
            background: var(--success-color);
            color: var(--white);
            box-shadow: var(--shadow-md);
        }

        .btn-success:hover:not(:disabled) {
            background: var(--success-light);
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
        }

        .btn-small {
            padding: 0.5rem 1rem;
            font-size: 0.85rem;
        }
<body>
    <div class="container">
        <h2>智能文本翻译工具</h2>
        <div class="config">
            <label for="apiUrl">API 端点 URL：</label>
            <input type="text" id="apiUrl" value="https://newapi.520952.xyz/v1" placeholder="输入 API 端点 URL（如 https://api.openai.com/v1）">
            <label for="apiKey">API 密钥：</label>
            <input type="password" id="apiKey" placeholder="输入 API 密钥">
            <button class="small-btn" onclick="fetchModels()">拉取模型列表</button>
            <label for="modelInput">选择模型：</label>
            <select id="modelInput" disabled>
                <option value="">请先拉取模型列表</option>
            </select>
        </div>
        <div class="text-box">
            <textarea id="inputText" placeholder="请输入要翻译的文本..."></textarea>
            <div class="divider"></div>
            <textarea id="outputText" readonly placeholder="翻译结果会显示在这里..."></textarea>
            <button class="copy-icon" onclick="copyToClipboard()" title="一键复制翻译结果">📋</button>
        </div>
        <button onclick="translateText()">翻译</button>
    </div>

    <script>
        async function fetchModels() {
            const apiUrl = document.getElementById('apiUrl').value;
            const apiKey = document.getElementById('apiKey').value;
            const modelSelect = document.getElementById('modelInput');

            if (!apiUrl) {
                alert('请输入 API 端点 URL');
                return;
            }
            if (!apiKey) {
                alert('请输入 API 密钥');
                return;
            }

            try {
                const modelsUrl = apiUrl.endsWith('/v1') ? `${apiUrl}/models` : `${apiUrl}/v1/models`;
                const response = await fetch(modelsUrl, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${apiKey}`
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                if (data.data && data.data.length > 0) {
                    modelSelect.innerHTML = '';
                    data.data.forEach(model => {
                        const option = document.createElement('option');
                        option.value = model.id;
                        option.textContent = model.id;
                        modelSelect.appendChild(option);
                    });
                    modelSelect.disabled = false;
                    alert(`模型列表拉取成功！共找到 ${data.data.length} 个模型。`);
                } else {
                    alert('未找到可用模型，请检查 API 端点和密钥。');
                    modelSelect.innerHTML = '<option value="">未找到模型</option>';
                    modelSelect.disabled = true;
                }
            } catch (error) {
                console.error('拉取模型列表出错:', error);
                alert('拉取模型列表失败，请检查网络、API 端点或密钥是否正确。');
                modelSelect.innerHTML = '<option value="">拉取失败</option>';
                modelSelect.disabled = true;
            }
        }

        async function translateText() {
            const inputText = document.getElementById('inputText').value;
            const apiUrl = document.getElementById('apiUrl').value;
            const apiKey = document.getElementById('apiKey').value;
            const modelInput = document.getElementById('modelInput').value;
            const outputText = document.getElementById('outputText');

            if (!inputText) {
                alert('请输入要翻译的文本');
                return;
            }
            if (!apiUrl) {
                alert('请输入 API 端点 URL');
                return;
            }
            if (!apiKey) {
                alert('请输入 API 密钥');
                return;
            }
            if (!modelInput) {
                alert('请选择模型');
                return;
            }

            try {
                const chatUrl = apiUrl.endsWith('/v1') ? `${apiUrl}/chat/completions` : apiUrl.includes('/v1/') ? apiUrl : `${apiUrl}/v1/chat/completions`;
                const response = await fetch(chatUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${apiKey}`
                    },
                    body: JSON.stringify({
                        model: modelInput,
                        messages: [
                            { role: 'system', content: '你是一个专业的翻译助手，直接将用户输入的中文翻译成英文，不要添加任何前缀或额外说明。' },
                            { role: 'user', content: inputText }
                        ],
                        temperature: 0.3
                    })
                });

                const data = await response.json();
                if (data.choices && data.choices.length > 0) {
                    outputText.value = data.choices[0].message.content;
                } else {
                    outputText.value = '翻译失败，请重试。';
                }
            } catch (error) {
                console.error('翻译出错:', error);
                outputText.value = '翻译过程中出现错误，请检查网络、API 端点、密钥或模型名称是否正确。';
            }
        }

        function copyToClipboard() {
            const outputText = document.getElementById('outputText');
            if (outputText.value.trim() === '') {
                alert('翻译结果为空，无法复制！');
                return;
            }
            outputText.select();
            try {
                document.execCommand('copy');
                alert('翻译结果已复制到剪贴板！');
            } catch (err) {
                console.error('复制失败:', err);
                alert('复制失败，请手动复制！');
            }
        }
    </script>
</body>
</html>