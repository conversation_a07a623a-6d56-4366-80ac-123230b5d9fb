@echo off
echo 正在启动智能翻译API服务...
echo.

REM 检查是否安装了Python
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误：未找到Python，请先安装Python 3.8或更高版本
    echo 下载地址：https://www.python.org/downloads/
    pause
    exit /b 1
)

REM 安装依赖包
echo 正在安装依赖包...
pip install -r requirements.txt

if errorlevel 1 (
    echo 依赖包安装失败，请检查网络连接或手动安装
    pause
    exit /b 1
)

echo.
echo 依赖包安装完成！
echo 正在启动FastAPI服务器...
echo.
echo 服务器将在以下地址启动：
echo - API文档：http://localhost:8000/docs
echo - 应用主页：http://localhost:8000
echo.
echo 要停止服务器，请按 Ctrl+C
echo.

REM 启动FastAPI服务器
python -m uvicorn main:app --host 0.0.0.0 --port 8000 --reload

pause 