from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse
from pydantic import BaseModel
import requests
import asyncio
from typing import Optional
import os

app = FastAPI(
    title="智能翻译API",
    description="支持英汉互译的FastAPI后端服务",
    version="1.0.0"
)

# 添加CORS中间件支持前端跨域请求
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境中应该设置具体的域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 配置API密钥和端点
API_KEY = "sk-3zwWOoF4K0fziVCD30k1DSMpH5sier9ZaHx5B63f65A03JOs"
API_BASE_URL = "https://newapi.520952.xyz/v1"
API_URL = f"{API_BASE_URL}/chat/completions"

class TranslateRequest(BaseModel):
    text: str
    source_lang: str  # "zh" 或 "en"
    target_lang: str  # "zh" 或 "en"
    model: Optional[str] = "gpt-3.5-turbo"  # 默认模型

class TranslateResponse(BaseModel):
    original_text: str
    translated_text: str
    source_language: str
    target_language: str

@app.get("/")
async def root():
    """根路径，重定向到前端页面"""
    return FileResponse("index.html")

@app.get("/api")
async def api_info():
    """API信息接口"""
    return {
        "message": "智能翻译API服务",
        "version": "1.0.0",
        "endpoints": {
            "translate": "/translate",
            "health": "/health"
        }
    }

@app.get("/health")
async def health_check():
    """健康检查接口"""
    return {"status": "healthy", "service": "translation-api"}

@app.get("/models")
async def get_models():
    """获取可用模型列表"""
    try:
        models_url = f"{API_BASE_URL}/models"
        headers = {
            "Authorization": f"Bearer {API_KEY}"
        }
        
        response = requests.get(models_url, headers=headers, timeout=10)
        response.raise_for_status()
        
        data = response.json()
        
        if "data" in data and isinstance(data["data"], list):
            # 直接返回获取到的所有模型，不做任何过滤
            models = []
            for model in data["data"]:
                models.append({
                    "id": model.get("id", ""),
                    "name": model.get("id", ""),  # 使用ID作为显示名称
                })
            
            return {"models": models}
        else:
            return {"models": [], "error": "API未返回模型数据"}
            
    except Exception as e:
        # 如果获取失败，返回空列表和错误信息
        return {
            "models": [],
            "error": f"无法获取模型列表: {str(e)}"
        }

@app.post("/translate", response_model=TranslateResponse)
async def translate_text(request: TranslateRequest):
    """
    翻译文本接口
    - 支持中文到英文翻译
    - 支持英文到中文翻译
    """
    if not request.text.strip():
        raise HTTPException(status_code=400, detail="文本内容不能为空")
    
    # 根据源语言和目标语言构建提示词
    if request.source_lang == "zh" and request.target_lang == "en":
        system_prompt = "你是一个专业的翻译助手。请将用户输入的中文翻译成自然流畅的英文，不要添加任何前缀、后缀或额外说明，只返回翻译结果。"
    elif request.source_lang == "en" and request.target_lang == "zh":
        system_prompt = "你是一个专业的翻译助手。请将用户输入的英文翻译成自然流畅的中文，不要添加任何前缀、后缀或额外说明，只返回翻译结果。"
    else:
        raise HTTPException(status_code=400, detail="不支持的语言组合")
    
    # 构建请求数据
    payload = {
        "model": request.model,  # 使用用户选择的模型
        "messages": [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": request.text}
        ],
        "temperature": 0.3,
        "max_tokens": 2000
    }
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {API_KEY}"
    }
    
    try:
        # 发送翻译请求
        response = requests.post(API_URL, json=payload, headers=headers, timeout=30)
        response.raise_for_status()
        
        data = response.json()
        
        if "choices" in data and len(data["choices"]) > 0:
            translated_text = data["choices"][0]["message"]["content"].strip()
            
            return TranslateResponse(
                original_text=request.text,
                translated_text=translated_text,
                source_language=request.source_lang,
                target_language=request.target_lang
            )
        else:
            raise HTTPException(status_code=500, detail="翻译服务返回数据格式错误")
            
    except requests.exceptions.Timeout:
        raise HTTPException(status_code=504, detail="翻译请求超时，请稍后重试")
    except requests.exceptions.RequestException as e:
        raise HTTPException(status_code=502, detail=f"翻译服务调用失败: {str(e)}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="127.0.0.1", port=8009)
