# 智能翻译工具 - AI Translation

一个基于FastAPI后端和现代前端技术的中英文互译应用。

## 🌟 特性

- **前后端分离架构** - FastAPI后端 + 现代化前端
- **双向翻译** - 支持中文→英文、英文→中文翻译
- **智能语言检测** - 自动识别输入语言
- **美观界面** - 采用现代化设计，响应式布局
- **实时翻译** - 快速响应，流畅交互
- **复制功能** - 一键复制翻译结果
- **字符统计** - 实时显示输入字符数

## 🚀 快速开始

### 方法一：使用启动脚本（推荐）

1. 双击运行 `start.bat` 文件
2. 脚本会自动安装依赖并启动服务
3. 服务启动后访问：`http://localhost:8000`

### 方法二：手动启动

#### 1. 安装依赖

```bash
pip install -r requirements.txt
```

#### 2. 启动后端服务

```bash
python -m uvicorn main:app --host 0.0.0.0 --port 8000 --reload
```

#### 3. 访问应用

- **前端界面**：http://localhost:8000/index.html
- **API文档**：http://localhost:8000/docs
- **健康检查**：http://localhost:8000/health

## 📁 项目结构

```
项目根目录/
├── main.py              # FastAPI后端主文件
├── index.html           # 前端页面
├── requirements.txt     # Python依赖包
├── start.bat           # Windows启动脚本
└── README.md           # 项目说明文档
```

## 🔧 技术栈

### 后端
- **FastAPI** - 高性能Python Web框架
- **Pydantic** - 数据验证和序列化
- **Uvicorn** - ASGI服务器
- **Requests** - HTTP客户端库

### 前端
- **HTML5** - 页面结构
- **CSS3** - 现代化样式设计
- **JavaScript** - 交互逻辑
- **Fetch API** - 异步HTTP请求

## 📚 API接口

### 翻译接口

**POST** `/translate`

请求体：
```json
{
  "text": "要翻译的文本",
  "source_lang": "zh",  // 源语言：zh(中文) 或 en(英文)
  "target_lang": "en"   // 目标语言：zh(中文) 或 en(英文)
}
```

响应：
```json
{
  "original_text": "原始文本",
  "translated_text": "翻译结果",
  "source_language": "zh",
  "target_language": "en"
}
```

### 健康检查

**GET** `/health`

响应：
```json
{
  "status": "healthy",
  "service": "translation-api"
}
```

## 🎯 使用说明

1. **选择语言方向**：点击中文/English按钮或使用交换按钮
2. **输入文本**：在左侧输入框输入要翻译的内容
3. **开始翻译**：点击"开始翻译"按钮或使用Ctrl+Enter快捷键
4. **查看结果**：翻译结果显示在右侧输出框
5. **复制结果**：点击📋按钮复制翻译结果到剪贴板
6. **清空文本**：点击"清空文本"按钮重置所有内容

## ⚙️ 配置说明

### API密钥
后端已集成翻译服务API密钥，无需额外配置。

### 服务端口
默认端口：8000
如需修改，请编辑 `main.py` 文件中的端口配置。

## 🔍 故障排除

### 常见问题

1. **Python未安装**
   - 下载并安装Python 3.8+：https://www.python.org/downloads/

2. **依赖安装失败**
   ```bash
   pip install --upgrade pip
   pip install -r requirements.txt
   ```

3. **端口被占用**
   ```bash
   # 更改端口为8001
   python -m uvicorn main:app --host 0.0.0.0 --port 8001 --reload
   ```

4. **翻译服务不可用**
   - 检查网络连接
   - 确认API服务正常运行

## 📄 许可证

本项目仅供学习和研究使用。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进项目。

---

**享受智能翻译带来的便利！** 🎉 